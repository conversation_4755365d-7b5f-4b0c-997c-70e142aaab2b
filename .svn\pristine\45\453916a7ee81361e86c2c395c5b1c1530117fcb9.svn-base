0000000000000000000000000000000000000000 8885ee5fbc52f637450e3002e2746f2275f4a546 liuchang1230 <<EMAIL>> 1741596018 +0800	branch: Created from HEAD
8885ee5fbc52f637450e3002e2746f2275f4a546 0de45b6e00c337088f003f55174c663d28d86418 liuchang1230 <<EMAIL>> 1741596093 +0800	commit: refactor: restructure project with modular architecture
0de45b6e00c337088f003f55174c663d28d86418 56374557f245e2442d677ea2e0eae2e3afdca4c4 liuchang1230 <<EMAIL>> 1745892535 +0800	commit: refactor: 重构代码结构，优化路径规划算法
56374557f245e2442d677ea2e0eae2e3afdca4c4 7a1eb5bd9a290ea6b8de579a39ec69bc7c78da30 liuchang1230 <<EMAIL>> 1746779954 +0800	commit: 修改 Settings 类，使其能够从多个位置查找配置文件，提高配置文件加载的灵活性。添加命令行参数支持，允许通过命令行指定关键配置参数：--config: 指定配置文件路径, --location: 指定地点（如 nanjing, shijiazhuang）, --server: 指定服务器地址。命令行参数的优先级高于配置文件中的设置，提供更灵活的配置方式。
