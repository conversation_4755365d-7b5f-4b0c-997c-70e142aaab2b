#include "map_handler_3d.h"
#include "cnpy.h"    // For loading .npz files - will need to be linked
#include <iostream>  // For logging warnings/errors
#include <cmath>     // For std::sqrt, std::round, std::floor, std::ceil
#include <algorithm> // For std::min, std::max
#include <vector>    // For std::vector in get_neighbors_grid

namespace CVToolbox
{

    MapHandler3DCpp::MapHandler3DCpp(const MapHandler3DConfig &config)
        : config_(config),
          grid_converter_(config.grid_converter_config),
          obstacle_manager_(config.map_height_grids, config.map_width_grids, config.map_depth_grids),
          polygon_index_2d_()
    { // Initialize polygon_index_2d

        if (config_.load_fixed_obstacles && !config_.fixed_obstacles_cache_path.empty())
        {
            load_fixed_obstacles_from_cache();
        }
    }

    // --- Core Grid Operations ---
    bool MapHandler3DCpp::is_in_bounds_grid(int y, int x, int z) const
    {
        return (y >= 0 && y < config_.map_height_grids &&
                x >= 0 && x < config_.map_width_grids &&
                z >= 0 && z < config_.map_depth_grids);
    }

    bool MapHandler3DCpp::is_traversable_grid(int y, int x, int z) const
    {
        if (!is_in_bounds_grid(y, x, z))
        {
            return false; // Out of bounds is not traversable
        }
        return non_traversable_.find({y, x, z}) == non_traversable_.end();
    }

    std::vector<GridPoint3D> MapHandler3DCpp::get_neighbors_grid(int y, int x, int z, bool allow_diagonal) const
    {
        std::vector<GridPoint3D> neighbors;
        neighbors.reserve(allow_diagonal ? 26 : 6);

        int dy_direct[] = {-1, 1, 0, 0, 0, 0};
        int dx_direct[] = {0, 0, -1, 1, 0, 0};
        int dz_direct[] = {0, 0, 0, 0, -1, 1};

        for (int i = 0; i < 6; ++i)
        {
            int ny = y + dy_direct[i];
            int nx = x + dx_direct[i];
            int nz = z + dz_direct[i];
            if (is_in_bounds_grid(ny, nx, nz))
            {
                neighbors.emplace_back(ny, nx, nz);
            }
        }

        if (allow_diagonal)
        {
            // 20 diagonal neighbors for 26-connectivity
            int d_diag[] = {-1, 1};
            // XY plane diagonals (z constant) - 4
            for (int dy_val : d_diag)
            {
                for (int dx_val : d_diag)
                {
                    if (is_in_bounds_grid(y + dy_val, x + dx_val, z))
                    {
                        neighbors.emplace_back(y + dy_val, x + dx_val, z);
                    }
                }
            }
            // XZ plane diagonals (y constant) - 4
            for (int dx_val : d_diag)
            {
                for (int dz_val : d_diag)
                {
                    if (is_in_bounds_grid(y, x + dx_val, z + dz_val))
                    {
                        neighbors.emplace_back(y, x + dx_val, z + dz_val);
                    }
                }
            }
            // YZ plane diagonals (x constant) - 4
            for (int dy_val : d_diag)
            {
                for (int dz_val : d_diag)
                {
                    if (is_in_bounds_grid(y + dy_val, x, z + dz_val))
                    {
                        neighbors.emplace_back(y + dy_val, x, z + dz_val);
                    }
                }
            }
            // True 3D diagonals (all change) - 8
            for (int dy_val : d_diag)
            {
                for (int dx_val : d_diag)
                {
                    for (int dz_val : d_diag)
                    {
                        if (is_in_bounds_grid(y + dy_val, x + dx_val, z + dz_val))
                        {
                            neighbors.emplace_back(y + dy_val, x + dx_val, z + dz_val);
                        }
                    }
                }
            }
        }
        return neighbors;
    }

    // --- Fixed Obstacles Loading ---
    void MapHandler3DCpp::load_fixed_obstacles_from_cache()
    {
        try
        {
            if (config_.fixed_obstacles_cache_path.empty())
            {
                std::cerr << "Warning: Fixed obstacles cache path is empty. Skipping loading." << std::endl;
                return;
            }

            cnpy::npz_t npz_map = cnpy::npz_load(config_.fixed_obstacles_cache_path);

            std::string key = "fixed_obstacles";
            if (npz_map.find(key) == npz_map.end())
            {
                if (npz_map.find("arr_0") != npz_map.end())
                    key = "arr_0";
                else
                {
                    std::cerr << "Warning: Key '" << key << "' (and 'arr_0') not found in NPZ file: "
                              << config_.fixed_obstacles_cache_path << std::endl;
                    return;
                }
            }

            cnpy::NpyArray arr = npz_map.at(key);
            if (arr.shape.size() != 2 || arr.shape[1] != 3)
            {
                std::cerr << "Warning: Fixed obstacles array in NPZ file has incorrect shape. Expected (N, 3)." << std::endl;
                return;
            }

            std::vector<GridPoint3D> fixed_points;
            fixed_points.reserve(arr.shape[0]);

            if (arr.word_size == 4 && (arr.type_char == 'i' || arr.type_char == 'l'))
            {
                int *data_ptr = arr.data<int>();
                for (size_t i = 0; i < arr.shape[0]; ++i)
                {
                    int y_coord = data_ptr[i * 3 + 0];
                    int x_coord = data_ptr[i * 3 + 1];
                    int z_coord = data_ptr[i * 3 + 2];
                    if (is_in_bounds_grid(y_coord, x_coord, z_coord))
                    {
                        fixed_points.emplace_back(y_coord, x_coord, z_coord);
                    }
                }
            }
            else if (arr.word_size == 8 && arr.type_char == 'l')
            {
                long *data_ptr = arr.data<long>();
                for (size_t i = 0; i < arr.shape[0]; ++i)
                {
                    int y_coord = static_cast<int>(data_ptr[i * 3 + 0]);
                    int x_coord = static_cast<int>(data_ptr[i * 3 + 1]);
                    int z_coord = static_cast<int>(data_ptr[i * 3 + 2]);
                    if (is_in_bounds_grid(y_coord, x_coord, z_coord))
                    {
                        fixed_points.emplace_back(y_coord, x_coord, z_coord);
                    }
                }
            }
            else
            {
                std::cerr << "Warning: Unsupported data type for fixed obstacles in NPZ: " << arr.type_char
                          << " with word size " << arr.word_size << std::endl;
                return;
            }

            if (!fixed_points.empty())
            {
                add_obstacle_points_to_map("fixed_obstacle", fixed_points, "Fixed building obstacles");
                std::cout << "Loaded " << fixed_points.size() << " fixed obstacle points from "
                          << config_.fixed_obstacles_cache_path << std::endl;
            }
        }
        catch (const std::runtime_error &e)
        {
            std::cerr << "Warning: Could not load fixed obstacles from cache '"
                      << config_.fixed_obstacles_cache_path << "'. Error: " << e.what() << std::endl;
        }
        catch (...)
        {
            std::cerr << "Warning: An unknown error occurred while loading fixed obstacles from cache '"
                      << config_.fixed_obstacles_cache_path << "'." << std::endl;
        }
    }

    // --- NFZ Management (Internal Helpers) ---
    void MapHandler3DCpp::add_obstacle_points_to_map(const std::string &type_name, const std::vector<GridPoint3D> &points, const std::string &description)
    {
        if (!obstacle_manager_.is_type_registered(type_name))
        {
            obstacle_manager_.register_type(type_name, description);
        }
        obstacle_manager_.add_positions_batch(type_name, points);
        non_traversable_.insert(points.begin(), points.end());
    }

    void MapHandler3DCpp::remove_obstacle_points_from_map(const std::string &type_name, const std::vector<GridPoint3D> &points)
    {
        if (!obstacle_manager_.is_type_registered(type_name))
        {
            return;
        }
        for (const auto &p : points)
        {
            obstacle_manager_.remove_position(type_name, p);
            if (obstacle_manager_.get_type_at_position(p).empty())
            {
                non_traversable_.erase(p);
            }
        }
    }

    // --- NFZ Management (Grid Coordinates directly) ---
    bool MapHandler3DCpp::add_solid_cylindrical_nfz_grid(
        const std::string &zone_id,
        const Point2D &center_yx_grid, double radius_grid_units,
        int min_z_grid, int max_z_grid,
        bool rasterize_boundary_only, const std::string &description)
    {

        if (nfz_definitions_.count(zone_id))
        {
            std::cerr << "Warning: NFZ with ID '" << zone_id << "' already exists. Remove it first." << std::endl;
            return false;
        }

        std::vector<GridPoint3D> nfz_points = rasterize_cylinder_3d(
            center_yx_grid, radius_grid_units, min_z_grid, max_z_grid, rasterize_boundary_only);

        if (!nfz_points.empty())
        {
            std::string obs_type_name = "nfz_" + zone_id;
            add_obstacle_points_to_map(obs_type_name, nfz_points, description);
        }

        nfz_definitions_[zone_id] = {
            zone_id, NFZShapeType::CYLINDER, {}, center_yx_grid, radius_grid_units, min_z_grid, max_z_grid, rasterize_boundary_only, description};
        return true;
    }

    bool MapHandler3DCpp::add_solid_polygon_nfz_grid(
        const std::string &zone_id,
        const Polygon2D &vertices_yx_grid,
        int min_z_grid, int max_z_grid,
        bool rasterize_boundary_only, const std::string &description)
    {

        if (nfz_definitions_.count(zone_id))
        {
            std::cerr << "Warning: NFZ with ID '" << zone_id << "' already exists. Remove it first." << std::endl;
            return false;
        }
        if (vertices_yx_grid.size() < 3)
        {
            std::cerr << "Warning: Polygon NFZ '" << zone_id << "' must have at least 3 vertices." << std::endl;
            return false;
        }

        std::vector<GridPoint3D> nfz_points = rasterize_polygon_3d(
            vertices_yx_grid, min_z_grid, max_z_grid, rasterize_boundary_only);

        if (!nfz_points.empty())
        {
            std::string obs_type_name = "nfz_" + zone_id;
            add_obstacle_points_to_map(obs_type_name, nfz_points, description);
        }

        polygon_index_2d_.add_polygon(zone_id, vertices_yx_grid);
        nfz_definitions_[zone_id] = {
            zone_id, NFZShapeType::POLYGON, vertices_yx_grid, {}, 0.0, min_z_grid, max_z_grid, rasterize_boundary_only, description};
        return true;
    }

    bool MapHandler3DCpp::remove_nfz(const std::string &zone_id)
    {
        auto it = nfz_definitions_.find(zone_id);
        if (it == nfz_definitions_.end())
        {
            return false;
        }
        std::string obs_type_name = "nfz_" + zone_id;

        std::set<GridPoint3D> points_to_remove_set = obstacle_manager_.get_positions_set(obs_type_name);
        std::vector<GridPoint3D> points_to_remove_vec(points_to_remove_set.begin(), points_to_remove_set.end());

        remove_obstacle_points_from_map(obs_type_name, points_to_remove_vec);
        obstacle_manager_.unregister_type(obs_type_name);

        if (it->second.shape_type == NFZShapeType::POLYGON)
        {
            polygon_index_2d_.remove_polygon(zone_id);
        }
        nfz_definitions_.erase(it);
        return true;
    }

    // --- NFZ Management (Geo Coordinates) ---
    bool MapHandler3DCpp::add_solid_cylindrical_nfz_geo(
        const std::string &zone_id,
        double center_lat, double center_lon, double radius_meters,
        double min_alt_msl, double max_alt_msl,
        bool rasterize_boundary_only, const std::string &description)
    {

        auto [cy_grid_double, cx_grid_double, cz_ref_grid_double] = grid_converter_.geo_to_relative_double(center_lat, center_lon, (min_alt_msl + max_alt_msl) / 2.0);
        Point2D center_yx_grid = {cy_grid_double, cx_grid_double};

        double cell_size_y_m = grid_converter_.get_config().cell_size_lat_deg * 111320.0;
        double cell_size_x_m = grid_converter_.get_config().cell_size_lon_deg * 111320.0 * std::cos(GridConverterCpp::to_radians(grid_converter_.get_config().ref_lat));
        double avg_cell_size_m = (cell_size_y_m + cell_size_x_m) / 2.0;
        if (avg_cell_size_m < 1e-6)
            return false;
        double radius_grid_units = radius_meters / avg_cell_size_m;

        auto [min_y_tmp, min_x_tmp, min_z_grid_double] = grid_converter_.geo_to_relative_double(center_lat, center_lon, min_alt_msl);
        auto [max_y_tmp, max_x_tmp, max_z_grid_double] = grid_converter_.geo_to_relative_double(center_lat, center_lon, max_alt_msl);
        int min_z_grid = static_cast<int>(std::round(min_z_grid_double));
        int max_z_grid = static_cast<int>(std::round(max_z_grid_double));

        return add_solid_cylindrical_nfz_grid(zone_id, center_yx_grid, radius_grid_units, min_z_grid, max_z_grid, rasterize_boundary_only, description);
    }

    bool MapHandler3DCpp::add_solid_polygon_nfz_geo(
        const std::string &zone_id,
        const std::vector<std::pair<double, double>> &vertices_lat_lon,
        double min_alt_msl, double max_alt_msl,
        double buffer_distance_meters,
        bool rasterize_boundary_only, const std::string &description)
    {

        Polygon2D vertices_yx_grid;
        vertices_yx_grid.reserve(vertices_lat_lon.size());
        for (const auto &lat_lon_pair : vertices_lat_lon)
        {
            auto [gy, gx, gz_tmp] = grid_converter_.geo_to_relative_double(lat_lon_pair.first, lat_lon_pair.second, min_alt_msl);
            vertices_yx_grid.push_back({gy, gx});
        }

        if (buffer_distance_meters > 0.0)
        {
            double cell_size_y_m = grid_converter_.get_config().cell_size_lat_deg * 111320.0;
            double cell_size_x_m = grid_converter_.get_config().cell_size_lon_deg * 111320.0 * std::cos(GridConverterCpp::to_radians(grid_converter_.get_config().ref_lat));
            double avg_cell_size_m = (cell_size_y_m + cell_size_x_m) / 2.0;
            if (avg_cell_size_m < 1e-6 && buffer_distance_meters > 0)
                return false;
            double buffer_grid_units = (avg_cell_size_m > 1e-6) ? (buffer_distance_meters / avg_cell_size_m) : 0.0;

            if (buffer_grid_units > 0)
            {
                vertices_yx_grid = GeometryUtils::expand_polygon(vertices_yx_grid, buffer_grid_units);
            }
        }

        auto [min_y_tmp, min_x_tmp, min_z_grid_double] = grid_converter_.geo_to_relative_double(vertices_lat_lon[0].first, vertices_lat_lon[0].second, min_alt_msl);
        auto [max_y_tmp, max_x_tmp, max_z_grid_double] = grid_converter_.geo_to_relative_double(vertices_lat_lon[0].first, vertices_lat_lon[0].second, max_alt_msl);
        int min_z_grid = static_cast<int>(std::round(min_z_grid_double));
        int max_z_grid = static_cast<int>(std::round(max_z_grid_double));

        return add_solid_polygon_nfz_grid(zone_id, vertices_yx_grid, min_z_grid, max_z_grid, rasterize_boundary_only, description);
    }

    // --- Collision/Conflict Checks ---
    bool MapHandler3DCpp::check_path_conflicts_with_obstacles_geo(const std::vector<Point3D> &path_geo_xyz) const
    {
        std::vector<GridPoint3D> path_grid_yxz;
        path_grid_yxz.reserve(path_geo_xyz.size());
        for (const auto &p_geo : path_geo_xyz)
        {
            auto [gy, gx, gz] = grid_converter_.geo_to_relative_int(p_geo.y, p_geo.x, p_geo.z);
            path_grid_yxz.emplace_back(gy, gx, gz);
        }
        return check_path_conflicts_with_obstacles_grid(path_grid_yxz);
    }

    bool MapHandler3DCpp::check_path_conflicts_with_obstacles_grid(const std::vector<GridPoint3D> &path_grid_yxz) const
    {
        for (const auto &p_grid : path_grid_yxz)
        {
            if (!is_traversable_grid(std::get<0>(p_grid), std::get<1>(p_grid), std::get<2>(p_grid)))
            {
                return true;
            }
        }
        return false;
    }

    bool MapHandler3DCpp::is_point_inside_any_nfz_2d_grid(const Point2D &point_yx_grid) const
    {
        std::vector<std::string> candidate_ids = polygon_index_2d_.get_candidate_polygons(point_yx_grid);
        for (const auto &id : candidate_ids)
        {
            const auto &nfz_def = nfz_definitions_.at(id);
            if (nfz_def.shape_type == NFZShapeType::POLYGON)
            {
                if (GeometryUtils::is_point_in_polygon_precise(point_yx_grid, nfz_def.vertices_grid_2d))
                {
                    return true;
                }
            }
        }
        for (const auto &pair : nfz_definitions_)
        {
            const auto &nfz_def = pair.second;
            if (nfz_def.shape_type == NFZShapeType::CYLINDER)
            {
                double dist_sq = GeometryUtils::distance_sq(point_yx_grid, nfz_def.center_grid_2d);
                if (dist_sq <= (nfz_def.radius_grid * nfz_def.radius_grid))
                {
                    return true;
                }
            }
        }
        return false;
    }

    std::vector<bool> MapHandler3DCpp::are_points_inside_any_nfz_2d_grid_batch(const std::vector<Point2D> &points_yx_grid) const
    {
        std::vector<bool> results;
        results.reserve(points_yx_grid.size());
        for (const auto &p : points_yx_grid)
        {
            results.push_back(is_point_inside_any_nfz_2d_grid(p));
        }
        return results;
    }

    std::vector<std::string> MapHandler3DCpp::get_nfz_ids_for_point_2d_grid(const Point2D &point_yx_grid) const
    {
        std::vector<std::string> conflicting_nfz_ids;
        std::vector<std::string> candidate_ids = polygon_index_2d_.get_candidate_polygons(point_yx_grid);
        for (const auto &id : candidate_ids)
        {
            const auto &nfz_def = nfz_definitions_.at(id);
            if (nfz_def.shape_type == NFZShapeType::POLYGON)
            {
                if (GeometryUtils::is_point_in_polygon_precise(point_yx_grid, nfz_def.vertices_grid_2d))
                {
                    conflicting_nfz_ids.push_back(id);
                }
            }
        }
        for (const auto &pair : nfz_definitions_)
        {
            const auto &nfz_def = pair.second;
            if (nfz_def.shape_type == NFZShapeType::CYLINDER)
            {
                bool already_checked = false;
                for (const auto &poly_id : candidate_ids)
                    if (poly_id == nfz_def.id)
                        already_checked = true;
                if (already_checked)
                    continue;

                double dist_sq = GeometryUtils::distance_sq(point_yx_grid, nfz_def.center_grid_2d);
                if (dist_sq <= (nfz_def.radius_grid * nfz_def.radius_grid))
                {
                    conflicting_nfz_ids.push_back(nfz_def.id);
                }
            }
        }
        return conflicting_nfz_ids;
    }

    // --- Rasterization Helpers ---
    std::vector<GridPoint3D> MapHandler3DCpp::rasterize_cylinder_3d(
        const Point2D &center_yx_grid, double radius_grid_units,
        int min_z_grid, int max_z_grid, bool boundary_only)
    {
        std::vector<GridPoint3D> points;
        double r_sq = radius_grid_units * radius_grid_units;

        int y_start_bbox = static_cast<int>(std::floor(center_yx_grid.y - radius_grid_units));
        int y_end_bbox = static_cast<int>(std::ceil(center_yx_grid.y + radius_grid_units));
        int x_start_bbox = static_cast<int>(std::floor(center_yx_grid.x - radius_grid_units));
        int x_end_bbox = static_cast<int>(std::ceil(center_yx_grid.x + radius_grid_units));

        for (int z = min_z_grid; z <= max_z_grid; ++z)
        {
            for (int y_grid = y_start_bbox; y_grid <= y_end_bbox; ++y_grid)
            {
                for (int x_grid = x_start_bbox; x_grid <= x_end_bbox; ++x_grid)
                {
                    if (!is_in_bounds_grid(y_grid, x_grid, z))
                        continue;

                    Point2D current_cell_center = {static_cast<double>(y_grid), static_cast<double>(x_grid)};
                    bool is_current_inside = GeometryUtils::distance_sq(current_cell_center, center_yx_grid) <= r_sq;

                    if (is_current_inside)
                    {
                        if (!boundary_only)
                        {
                            points.emplace_back(y_grid, x_grid, z);
                        }
                        else
                        {
                            bool is_boundary = false;
                            int dy_neighbors[] = {-1, 1, 0, 0};
                            int dx_neighbors[] = {0, 0, -1, 1};
                            for (int i = 0; i < 4; ++i)
                            {
                                int ny = y_grid + dy_neighbors[i];
                                int nx = x_grid + dx_neighbors[i];
                                // Check if neighbor is within map bounds before checking if it's outside shape
                                if (!is_in_bounds_grid(ny, nx, z))
                                {
                                    is_boundary = true; // Edge of map is a boundary
                                    break;
                                }
                                Point2D neighbor_cell_center = {static_cast<double>(ny), static_cast<double>(nx)};
                                if (GeometryUtils::distance_sq(neighbor_cell_center, center_yx_grid) > r_sq)
                                {
                                    is_boundary = true;
                                    break;
                                }
                            }
                            if (is_boundary)
                            {
                                points.emplace_back(y_grid, x_grid, z);
                            }
                        }
                    }
                }
            }
        }
        return points;
    }

    std::vector<GridPoint3D> MapHandler3DCpp::rasterize_polygon_3d(
        const Polygon2D &vertices_yx_grid,
        int min_z_grid, int max_z_grid, bool boundary_only)
    {
        std::vector<GridPoint3D> points;
        if (vertices_yx_grid.empty())
            return points;

        BoundingBox2D bbox(vertices_yx_grid);

        for (int z = min_z_grid; z <= max_z_grid; ++z)
        {
            for (int y_grid = static_cast<int>(std::floor(bbox.min_y)); y_grid <= static_cast<int>(std::ceil(bbox.max_y)); ++y_grid)
            {
                for (int x_grid = static_cast<int>(std::floor(bbox.min_x)); x_grid <= static_cast<int>(std::ceil(bbox.max_x)); ++x_grid)
                {
                    if (!is_in_bounds_grid(y_grid, x_grid, z))
                        continue;

                    Point2D current_cell_center = {static_cast<double>(y_grid), static_cast<double>(x_grid)};
                    bool is_current_inside = GeometryUtils::is_point_in_polygon_precise(current_cell_center, vertices_yx_grid);

                    if (is_current_inside)
                    {
                        if (!boundary_only)
                        {
                            points.emplace_back(y_grid, x_grid, z);
                        }
                        else
                        {
                            bool is_boundary = false;
                            int dy_neighbors[] = {-1, 1, 0, 0};
                            int dx_neighbors[] = {0, 0, -1, 1};
                            for (int i = 0; i < 4; ++i)
                            {
                                int ny = y_grid + dy_neighbors[i];
                                int nx = x_grid + dx_neighbors[i];
                                if (!is_in_bounds_grid(ny, nx, z))
                                {
                                    is_boundary = true;
                                    break;
                                }
                                Point2D neighbor_cell_center = {static_cast<double>(ny), static_cast<double>(nx)};
                                if (!GeometryUtils::is_point_in_polygon_precise(neighbor_cell_center, vertices_yx_grid))
                                {
                                    is_boundary = true;
                                    break;
                                }
                            }
                            if (is_boundary)
                            {
                                points.emplace_back(y_grid, x_grid, z);
                            }
                        }
                    }
                }
            }
        }
        return points;
    }

} // namespace CVToolbox
