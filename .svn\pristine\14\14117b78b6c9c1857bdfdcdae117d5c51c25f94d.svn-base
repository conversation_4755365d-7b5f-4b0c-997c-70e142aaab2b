x߇ xblob 34772 from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple
import time
import json
import threading
import random
from datetime import datetime
import paho.mqtt.client as mqtt
import mysql.connector
from ...config.settings import settings
from ...core.map.map_handler_3d import Map3D
from ...core.map.occupancy_map import OccupancyMap
from ...core.pathfinding.high_level_policy_3d import CBS3D
from ...utils.logging import get_logger
from ...utils.visualization import FlightPathVisualizer

logger = get_logger(__name__)


class MessageHandler(ABC):
    """消息处理器基类"""

    # 类变量，所有实例共享
    map = None
    occupancy_map = None
    grid_converter = None
    producer = None
    uav_topic = None
    response_topic = None
    response_topic_sora = None
    planner = None
    planned_paths = {}  # 存储所有已规划路径
    visualizer = None  # 路径可视化器

    mqtt_client = None  # MQTT客户端实例
    db_conn = None  # 数据库连接实例
    db_conn_last_active = None  # 记录最后活动时间
    db_conn_check_interval = 30  # 数据库连接检查间隔（秒），降低为30秒以更频繁检查
    db_conn_check_timer = None  # 数据库连接检查定时器

    def __init__(
        self,
        map_3d: Map3D,
        occupancy_map: OccupancyMap,
        producer=None,
        response_topic=None,
        response_topic_sora=None,
    ):
        """
        初始化消息处理器

        Args:
            map_3d: 3D地图实例
            occupancy_map: 占用图实例
            producer: Kafka生产者实例
            response_topic: 响应前端主题
            response_topic_sora: SORA响应主题
        """
        # 只在第一个实例初始化时设置类变量
        if MessageHandler.map is None:
            MessageHandler.map = map_3d
            MessageHandler.occupancy_map = occupancy_map
            MessageHandler.grid_converter = map_3d.grid_converter
            MessageHandler.producer = producer
            MessageHandler.uav_topic = settings.kafka.uav_topic
            MessageHandler.uav_turning_topic = settings.kafka.uav_turning_topic
            MessageHandler.response_topic = response_topic
            MessageHandler.response_topic_sora = response_topic_sora
            MessageHandler.planner = CBS3D(
                MessageHandler.map, MessageHandler.occupancy_map
            )

            # 初始化可视化器
            if MessageHandler.visualizer is None:
                MessageHandler.visualizer = FlightPathVisualizer(MessageHandler.map)

            # 初始化MQTT客户端(如果启用)
            if settings.mqtt.enabled and MessageHandler.mqtt_client is None:
                self._init_mqtt_client()

            # 初始化数据库连接
            self._init_db_conn()

            # 启动数据库连接定期检查
            self._start_db_conn_check_timer()

            # 启动连接池清理定时器
            self._start_db_pool_cleanup_timer()

    def _init_db_conn(self):
        """初始化数据库连接"""
        try:
            if MessageHandler.db_conn is None:
                # 创建连接配置字典
                conn_config = {
                    "host": settings.route_database.host,
                    "port": settings.route_database.port,
                    "user": settings.route_database.user,
                    "password": settings.route_database.password,
                    "database": settings.route_database.database,
                    "pool_size": 32,  # 设置为最大允许值32
                    "pool_name": "route_pool",
                    # 添加连接配置
                    "autocommit": True,  # 自动提交
                    "buffered": True,  # 缓冲查询结果
                    # 连接重试设置
                    "get_warnings": True,
                    "raise_on_warnings": True,
                    # 保活设置
                    "connection_timeout": 120,  # 增加连接超时时间到12秒
                    # 添加连接池设置
                    "pool_reset_session": True,  # 重置会话状态
                    # 使用纯Python实现，避免SSL相关问题
                    "use_pure": True,
                    # 增加连接重试参数
                    "connect_timeout": 30,  # 连接超时时间
                    # 注意：MySQL Connector/Python不支持pool_queue_size和pool_queue_timeout参数
                }

                # 创建数据库连接
                MessageHandler.db_conn = mysql.connector.connect(**conn_config)
                MessageHandler.db_conn_last_active = time.time()
                logger.info("自动路径规划数据库连接初始化成功")
        except Exception as e:
            logger.error(f"自动路径规划数据库连接初始化失败: {str(e)}")
            MessageHandler.db_conn = None

    def _check_db_conn(self):
        """检查并维护数据库连接"""
        if MessageHandler.db_conn is None:
            logger.debug("自动路径规划数据库连接不存在或已断开，尝试初始化")
            self._init_db_conn()
            return bool(MessageHandler.db_conn)

        # 检查连接是否超时或失效
        try:
            # 使用一个简单的查询测试连接
            with MessageHandler.db_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            MessageHandler.db_conn_last_active = time.time()
            return True
        except Exception as e:
            logger.warning(f"自动路径规划数据库连接测试失败: {str(e)}")
            try:
                # 尝试释放连接池中的所有连接
                if hasattr(MessageHandler.db_conn, "_cnx_pool"):
                    logger.info("尝试释放连接池中的所有连接")
                    pool = MessageHandler.db_conn._cnx_pool
                    if pool:
                        pool.reset_session = True
                        pool.set_config(**pool._cnx_config)
                MessageHandler.db_conn.close()
            except Exception as close_error:
                logger.error(f"关闭数据库连接失败: {str(close_error)}")
            finally:
                MessageHandler.db_conn = None
                # 等待一秒后再尝试初始化新连接
                time.sleep(1)
                self._init_db_conn()
                return bool(MessageHandler.db_conn)

        # return True

    def _monitor_pool_usage(self):
        """监控连接池使用情况"""
        if MessageHandler.db_conn is None:
            return 0

        try:
            # 尝试获取连接池信息
            if hasattr(MessageHandler.db_conn, "_cnx_pool"):
                pool = MessageHandler.db_conn._cnx_pool
                if pool and hasattr(pool, "_queue_free"):
                    # 获取空闲连接数量
                    free_connections = len(pool._queue_free)
                    total_connections = pool._pool_size
                    used_connections = total_connections - free_connections
                    usage_percent = (used_connections / total_connections) * 100

                    # 根据使用率决定日志级别
                    if usage_percent > 90:
                        logger.warning(
                            f"连接池使用率过高: {used_connections}/{total_connections} ({usage_percent:.1f}%)"
                        )
                    elif usage_percent > 70:
                        logger.info(
                            f"连接池使用率较高: {used_connections}/{total_connections} ({usage_percent:.1f}%)"
                        )
                    else:
                        logger.debug(
                            f"连接池使用情况: {used_connections}/{total_connections} ({usage_percent:.1f}%)"
                        )

                    # 返回使用百分比
                    return usage_percent

                # 如果没有_queue_free属性，尝试获取其他信息
                pool_info = {
                    "pool_name": getattr(pool, "pool_name", "unknown"),
                    "pool_size": getattr(pool, "_pool_size", 0),
                    "pool_max_size": getattr(pool, "_max_pool_size", 0),
                }
                # logger.info(f"连接池信息: {pool_info}")
        except Exception as e:
            logger.warning(f"监控连接池使用情况失败: {str(e)}")

        return 0  # 默认返回0

    def _cleanup_db_pool(self):
        """清理数据库连接池中的空闲连接"""
        if MessageHandler.db_conn is None:
            return

        try:
            # 先检查连接池使用情况
            usage_percent = self._monitor_pool_usage()

            # 如果使用率超过80%，则强制重新初始化连接池
            if usage_percent > 80:
                logger.warning(
                    f"连接池使用率过高 ({usage_percent:.1f}%)，强制重新初始化"
                )

            # 尝试释放连接并重新初始化
            logger.info("尝试清理数据库连接池")

            # 先关闭当前连接
            try:
                MessageHandler.db_conn.close()
            except Exception as close_error:
                logger.warning(f"关闭数据库连接失败: {str(close_error)}")

            # 等待短暂停止后重新初始化连接
            time.sleep(0.5)
            MessageHandler.db_conn = None
            self._init_db_conn()

            if MessageHandler.db_conn:
                logger.info("数据库连接池重新初始化成功")
            else:
                logger.warning("数据库连接池重新初始化失败")
        except Exception as e:
            logger.error(f"清理数据库连接池失败: {str(e)}")

    # 添加一个新的定时器属性用于连接池清理
    db_pool_cleanup_timer = None
    db_pool_cleanup_interval = 10  # 每10秒清理一次连接池

    def _start_db_pool_cleanup_timer(self):
        """启动连接池定期清理定时器"""
        if MessageHandler.db_pool_cleanup_timer is not None:
            return  # 定时器已经启动

        def periodic_cleanup():
            """定期清理连接池"""
            try:
                # 监控连接池使用情况
                usage_percent = self._monitor_pool_usage()

                # 根据使用率决定清理策略
                if usage_percent > 80:
                    logger.warning(
                        f"连接池使用率过高 ({usage_percent:.1f}%)，执行强制清理"
                    )
                    self._cleanup_db_pool()
                elif usage_percent > 50:
                    logger.info(
                        f"连接池使用率较高 ({usage_percent:.1f}%)，执行常规清理"
                    )
                    self._cleanup_db_pool()
                elif random.random() < 0.2:  # 20%的概率执行清理
                    logger.debug("执行随机连接池清理")
                    self._cleanup_db_pool()
            except Exception as e:
                logger.error(f"定期连接池清理失败: {str(e)}")
            finally:
                # 重新设置定时器
                MessageHandler.db_pool_cleanup_timer = threading.Timer(
                    MessageHandler.db_pool_cleanup_interval, periodic_cleanup
                )
                MessageHandler.db_pool_cleanup_timer.daemon = True
                MessageHandler.db_pool_cleanup_timer.start()

        # 启动第一个定时器
        MessageHandler.db_pool_cleanup_timer = threading.Timer(
            MessageHandler.db_pool_cleanup_interval, periodic_cleanup
        )
        MessageHandler.db_pool_cleanup_timer.daemon = True
        MessageHandler.db_pool_cleanup_timer.start()
        logger.info(
            f"已启动连接池定期清理，间隔: {MessageHandler.db_pool_cleanup_interval}秒"
        )

    def _start_db_conn_check_timer(self):
        """启动数据库连接定期检查定时器"""
        if MessageHandler.db_conn_check_timer is not None:
            return  # 定时器已经启动

        def periodic_check():
            """定期检查数据库连接"""
            try:
                logger.debug("执行定期数据库连接检查")
                self._check_db_conn()

                # 监控连接池使用情况
                self._monitor_pool_usage()

                # 确保连接池清理定时器已启动
                if MessageHandler.db_pool_cleanup_timer is None:
                    self._start_db_pool_cleanup_timer()

                # 重新设置定时器
                MessageHandler.db_conn_check_timer = threading.Timer(
                    MessageHandler.db_conn_check_interval, periodic_check
                )
                MessageHandler.db_conn_check_timer.daemon = True  # 设为守护线程
                MessageHandler.db_conn_check_timer.start()
            except Exception as e:
                logger.error(f"定期数据库连接检查失败: {str(e)}")

        # 启动第一个定时器
        MessageHandler.db_conn_check_timer = threading.Timer(
            MessageHandler.db_conn_check_interval, periodic_check
        )
        MessageHandler.db_conn_check_timer.daemon = True  # 设为守护线程
        MessageHandler.db_conn_check_timer.start()
        logger.info(
            f"已启动数据库连接定期检查，间隔: {MessageHandler.db_conn_check_interval}秒"
        )

        # 同时启动连接池清理定时器
        self._start_db_pool_cleanup_timer()

    @abstractmethod
    def handle_message(self, message: Dict) -> Tuple[Optional[List], Optional[str]]:
        """
        处理消息的抽象方法

        Args:
            message: 消息字典

        Returns:
            Tuple[Optional[List], Optional[str]]: (路径点列表, 错误信息)
        """
        pass

    def parse_time(self, time_str: str) -> int:
        """
        将时间字符串转换为Unix时间戳

        Args:
            time_str: 格式为 "YYYY-MM-DD HH:MM:SS" 的时间字符串

        Returns:
            int: Unix时间戳（秒）
        """
        dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        return int(dt.timestamp())

    def parse_point(self, point_str: str) -> Dict:
        """
        解析坐标字符串为经纬度和高度

        Args:
            point_str: 格式为 "lon,lat,alt" 的坐标字符串

        Returns:
            Dict: 包含经纬度和高度的字典
        """
        lon, lat, alt = map(float, point_str.split(","))
        return {"lat": lat, "lon": lon, "alt": alt}

    def store_path(
        self,
        flight_id: str,
        path_nodes: List,
        turn_path_nodes: List,
        request_info: Dict,
    ) -> None:
        """
        存储已规划的路径

        Args:
            flight_id: 航班ID
            path_nodes: 完整node路径
            turn_path_nodes: 拐点node路径
            request_info: 请求相关信息
        """
        full_info = request_info.copy()
        full_info["path_nodes"] = path_nodes  # 完整node路径
        full_info["turn_path_nodes"] = turn_path_nodes  # 拐点node路径
        MessageHandler.planned_paths[flight_id] = full_info

        # 将路径添加到可视化器
        # if MessageHandler.visualizer:
        #     MessageHandler.visualizer.add_path(flight_id, grid_coords)

    def visualize_paths(self):
        """可视化所有已规划的路径和禁飞区"""
        if MessageHandler.visualizer:
            MessageHandler.visualizer.show()

    def _init_mqtt_client(self):
        """初始化MQTT客户端"""
        try:
            MessageHandler.mqtt_client = mqtt.Client()
            MessageHandler.mqtt_client.username_pw_set(
                settings.mqtt.username, settings.mqtt.password
            )
            # 解析broker地址和端口
            host, port = settings.mqtt.broker.split(":")
            MessageHandler.mqtt_client.connect(host, int(port), 60)
            MessageHandler.mqtt_client.loop_start()
            logger.info("MQTT客户端初始化成功")
        except Exception as e:
            logger.error(f"MQTT客户端初始化失败: {str(e)}")
            MessageHandler.mqtt_client = None

    def _send_mqtt(self, topic: str, message: Dict) -> None:
        """通过MQTT发送消息"""
        if not MessageHandler.mqtt_client:
            logger.error("MQTT客户端未初始化")
            return

        try:
            payload = json.dumps(message)
            result = MessageHandler.mqtt_client.publish(topic, payload)
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"MQTT消息发送成功: {topic}")
            else:
                logger.error(f"MQTT消息发送失败: {result.rc}")
        except Exception as e:
            logger.error(f"MQTT消息发送错误: {str(e)}")

    def _send_response(
        self,
        request: Dict,  # 原始请求消息
        new_data: Dict,  # 新的数据（会覆盖原始消息中的同名字段）
        response_topic: str,  # 可选的响应主题
    ) -> None:
        """
        发送响应消息，直接合并原始消息和新数据

        Args:
            request: 原始请求消息（完整保留）
            new_data: 新的数据（会覆盖原始消息中的同名字段）
            response_topic: 可选的响应主题
        """
        # 直接复制原始消息
        response = request.copy()

        # 更新新的数据（直接覆盖）
        response.update(new_data)

        # 确保时间戳更新（如果新数据中没有指定）
        if "timestamp" not in new_data:
            response["timestamp"] = int(time.time())

        # 检查是否使用MQTT发送给无人机
        # if settings.mqtt.enabled and response_topic == MessageHandler.uav_topic:
        if settings.mqtt.enabled:
            self._send_mqtt(response_topic, response)
        else:
            # 使用Kafka发送
            MessageHandler.producer.send(response_topic, response)
            MessageHandler.producer.flush()
            logger.info(f"已通过Kafka发送响应到{response_topic}: {response}")

    def _clear_all_routes(self):
        """清空所有航线数据"""
        if not self._check_db_conn():
            logger.error("数据库连接不可用，无法清空数据")
            return False

        try:
            with MessageHandler.db_conn.cursor() as cursor:
                # 清空路径点表
                cursor.execute("TRUNCATE TABLE route_node_manage")
                # 清空航线表
                cursor.execute("TRUNCATE TABLE flight_route_manage")

            MessageHandler.db_conn.commit()
            logger.info("成功清空所有航线数据")
            return True
        except Exception as e:
            logger.error(f"清空航线数据失败: {str(e)}")
            if MessageHandler.db_conn:
                MessageHandler.db_conn.rollback()
            return False

    def _execute_with_retry(self, operation):
        """执行数据库操作，带有增强的重试机制"""
        max_retries = 5  # 增加重试次数
        retry_count = 0
        last_error = None

        while retry_count < max_retries:
            try:
                # 检查连接状态
                if not self._check_db_conn():
                    # 如果连接不可用，尝试重新初始化
                    logger.warning("数据库连接不可用，尝试重新初始化")
                    self._cleanup_db_pool()  # 清理连接池并重新初始化

                    if not self._check_db_conn():
                        raise Exception("数据库连接不可用，重新初始化失败")

                # 执行操作
                result = operation()

                # 操作完成后确保连接仍然有效
                self._check_db_conn()

                return result

            except Exception as e:
                last_error = e
                retry_count += 1

                # 检查是否是SSL相关错误或连接丢失错误
                error_str = str(e).lower()
                is_connection_error = (
                    "lost connection" in error_str
                    or "ssl" in error_str
                    or "connection" in error_str
                    or "timeout" in error_str
                    or "not available" in error_str
                )

                if is_connection_error:
                    # 如果是连接错误，尝试重新初始化连接
                    logger.warning(f"检测到连接错误，尝试重新初始化连接: {error_str}")
                    self._cleanup_db_pool()

                logger.warning(
                    f"数据库操作失败 (尝试 {retry_count}/{max_retries}): {str(e)}"
                )

                if retry_count < max_retries:
                    # 根据重试次数增加等待时间
                    wait_time = retry_count * 2  # 每次重试增加等待时间
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)  # 等待时间逐次增加
                else:
                    logger.error(f"数据库操作失败，已重试{max_retries}次，放弃操作")
                    raise Exception(
                        f"数据库操作失败，已重试{max_retries}次: {str(last_error)}"
                    )

    @classmethod
    def initialize_connections(cls):
        """在系统启动时初始化所有连接"""
        # 初始化数据库连接
        if cls.db_conn is None:
            try:
                # 创建连接配置字典
                conn_config = {
                    "host": settings.route_database.host,
                    "port": settings.route_database.port,
                    "user": settings.route_database.user,
                    "password": settings.route_database.password,
                    "database": settings.route_database.database,
                    "pool_size": 32,  # 设置为最大允许值32
                    "pool_name": "route_pool",
                    # 添加连接配置
                    "autocommit": True,  # 自动提交
                    "buffered": True,  # 缓冲查询结果
                    # 连接重试设置
                    "get_warnings": True,
                    "raise_on_warnings": True,
                    # 保活设置
                    "connection_timeout": 120,  # 增加连接超时时间到12秒
                    # 添加连接池设置
                    "pool_reset_session": True,  # 重置会话状态
                    # 使用纯Python实现，避免SSL相关问题
                    "use_pure": True,
                    # 增加连接重试参数
                    "connect_timeout": 30,  # 连接超时时间
                    # 注意：MySQL Connector/Python不支持pool_queue_size和pool_queue_timeout参数
                }

                # 创建数据库连接
                cls.db_conn = mysql.connector.connect(**conn_config)
                cls.db_conn_last_active = time.time()
                logger.info("系统启动时初始化数据库连接成功")

                # 测试连接
                with cls.db_conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                logger.info("数据库连接测试成功")
            except Exception as e:
                logger.error(f"系统启动时初始化数据库连接失败: {str(e)}")
                cls.db_conn = None

        # 初始化MQTT客户端
        if settings.mqtt.enabled and cls.mqtt_client is None:
            try:
                cls.mqtt_client = mqtt.Client()
                cls.mqtt_client.username_pw_set(
                    settings.mqtt.username, settings.mqtt.password
                )
                # 解析broker地址和端口
                host, port = settings.mqtt.broker.split(":")
                cls.mqtt_client.connect(host, int(port), 60)
                cls.mqtt_client.loop_start()
                logger.info("系统启动时初始化MQTT客户端成功")
            except Exception as e:
                logger.error(f"系统启动时初始化MQTT客户端失败: {str(e)}")
                cls.mqtt_client = None

    @classmethod
    def close_connections(cls):
        """关闭所有连接和定时器"""
        # 停止连接池清理定时器
        if cls.db_pool_cleanup_timer:
            try:
                cls.db_pool_cleanup_timer.cancel()
                cls.db_pool_cleanup_timer = None
                logger.info("连接池清理定时器已停止")
            except Exception as e:
                logger.error(f"停止连接池清理定时器失败: {str(e)}")

        # 停止数据库连接检查定时器
        if cls.db_conn_check_timer:
            try:
                cls.db_conn_check_timer.cancel()
                cls.db_conn_check_timer = None
                logger.info("数据库连接检查定时器已停止")
            except Exception as e:
                logger.error(f"停止数据库连接检查定时器失败: {str(e)}")

        # 关闭数据库连接
        if cls.db_conn:
            try:
                # 直接关闭连接，不尝试操作连接池
                cls.db_conn.close()
                cls.db_conn = None
                logger.info("自动路径规划数据库连接已关闭")
            except Exception as e:
                logger.error(f"关闭数据库连接失败: {str(e)}")

        # 关闭MQTT客户端
        if cls.mqtt_client:
            try:
                cls.mqtt_client.loop_stop()
                cls.mqtt_client.disconnect()
                cls.mqtt_client = None
                logger.info("MQTT客户端已关闭")
            except Exception as e:
                logger.error(f"关闭MQTT客户端失败: {str(e)}")

        # 关闭可视化器
        if cls.visualizer:
            try:
                cls.visualizer.close()
                cls.visualizer = None
                logger.info("路径可视化器已关闭")
            except Exception as e:
                logger.error(f"关闭路径可视化器失败: {str(e)}")

        # 最后等待一秒，确保所有资源都已释放
        time.sleep(1)

    def _save_to_db(
        self,
        route_id: int,
        route_name: str,
        estimated_time: int,
        all_distance: int,
        path_geo: List[Dict],
        is_update: bool = False,
        batch_size: int = 10,  # 进一步减小批处理大小，更频繁地释放连接
    ):
        """使用持久连接保存数据到数据库"""
        # 清空表格
        # self._clear_all_routes()

        # 创建一个字典来存储状态，这样内部函数可以修改它
        state = {"is_update": is_update}

        def db_operation():
            # 创建一个新的连接，而不是使用共享的连接
            # 这样可以确保操作完成后连接被完全释放
            conn_config = {
                "host": settings.route_database.host,
                "port": settings.route_database.port,
                "user": settings.route_database.user,
                "password": settings.route_database.password,
                "database": settings.route_database.database,
                "autocommit": False,  # 开始时关闭自动提交
                "buffered": True,
                "use_pure": True,
            }

            # 使用with语句确保连接在操作完成后自动关闭
            with mysql.connector.connect(**conn_config) as conn:
                try:
                    with conn.cursor() as cursor:
                        # 首先检查记录是否已存在
                        cursor.execute(
                            "SELECT COUNT(*) FROM flight_route_manage WHERE id = %s",
                            (route_id,),
                        )
                        record_exists = cursor.fetchone()[0] > 0

                        # 如果记录存在且不是更新操作，记录日志并返回
                        if record_exists and not state["is_update"]:
                            logger.warning(
                                f"航线ID {route_id} 已存在，但未标记为更新操作。将强制执行更新操作。"
                            )
                            state["is_update"] = True

                        # 如果是更新操作或记录已存在，先删除旧数据
                        if state["is_update"] or record_exists:
                            logger.info(f"删除航线ID {route_id} 的现有数据")
                            cursor.execute(
                                "DELETE FROM route_node_manage WHERE flight_route_id = %s",
                                (route_id,),
                            )
                            cursor.execute(
                                "DELETE FROM flight_route_manage WHERE id = %s",
                                (route_id,),
                            )

                        # 插入航线管理记录
                        logger.info(
                            f"插入航线管理记录，ID: {route_id}, 名称: {route_name}"
                        )
                        cursor.execute(
                            "INSERT INTO flight_route_manage "
                            "(id, name, estimated_time, all_distance) "
                            "VALUES (%s, %s, %s, %s)",
                            (route_id, route_name, estimated_time, all_distance),
                        )

                        # 提交航线管理记录
                        conn.commit()

                        # 准备批量插入数据
                        batch_data = [
                            (
                                route_id,
                                point["index"],
                                point["lng"],
                                point["lat"],
                                point["height"],
                            )
                            for point in path_geo
                        ]

                        total_points = len(batch_data)
                        total_batches = (total_points - 1) // batch_size + 1

                        # 分批处理
                        for i in range(0, total_points, batch_size):
                            current_batch = batch_data[i : i + batch_size]
                            try:
                                cursor.executemany(
                                    "INSERT INTO route_node_manage "
                                    "(flight_route_id, node_index, longitude, latitude, height) "
                                    "VALUES (%s, %s, %s, %s, %s)",
                                    current_batch,
                                )
                                # 每批次提交一次，避免单个事务过大
                                conn.commit()
                                batch_num = i // batch_size + 1
                                logger.debug(
                                    f"已保存批次 {batch_num}/{total_batches}，进度: {min(i+batch_size, total_points)}/{total_points}"
                                )
                            except Exception as batch_error:
                                # 如果批处理失败，回滚并记录错误
                                conn.rollback()
                                logger.error(
                                    f"批量插入数据失败 (批次 {batch_num}/{total_batches}): {str(batch_error)}"
                                )
                                # 重新抛出异常以触发重试机制
                                raise

                        logger.info(
                            f"成功保存路径数据到自动路径规划数据库，route_id: {route_id}，共 {total_points} 个点"
                        )

                        # 更新最后活动时间
                        MessageHandler.db_conn_last_active = time.time()
                        return True

                except Exception as e:
                    # 发生异常时回滚事务
                    conn.rollback()
                    logger.error(f"数据库操作异常，已回滚: {str(e)}")
                    raise

        try:
            # 执行数据库操作，使用独立连接
            result = self._execute_with_retry(db_operation)
            return result
        except Exception as e:
            logger.error(f"保存路径数据失败: {str(e)}")

            # 检查是否是SSL相关错误或连接丢失错误
            error_str = str(e).lower()
            is_connection_error = (
                "lost connection" in error_str
                or "ssl" in error_str
                or "connection" in error_str
                or "timeout" in error_str
                or "not available" in error_str
                or "pool exhausted" in error_str
            )

            if is_connection_error:
                # 如果是连接错误，尝试重新初始化连接池
                logger.warning(f"检测到连接错误，尝试重新初始化连接池: {error_str}")
                self._cleanup_db_pool()

                # 如果是连接池耗尽错误，等待一段时间后重试
                if "pool exhausted" in error_str:
                    logger.warning("连接池耗尽，等待3秒后重试...")
                    time.sleep(3)
                    try:
                        # 再次尝试执行操作
                        return self._execute_with_retry(db_operation)
                    except Exception as retry_error:
                        logger.error(f"重试保存路径数据失败: {str(retry_error)}")

            # 确保共享连接池也处于正常状态
            if MessageHandler.db_conn:
                try:
                    # 确保连接回到正常状态
                    MessageHandler.db_conn.rollback()
                    MessageHandler.db_conn.autocommit = True
                except Exception as rollback_error:
                    # 如果回滚失败，尝试重新初始化连接
                    logger.error(f"回滚事务失败: {str(rollback_error)}")
                    self._cleanup_db_pool()

            # 返回失败
            return False
uOÎ