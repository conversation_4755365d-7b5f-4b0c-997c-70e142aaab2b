# Details

Date : 2025-04-16 15:30:18

Directory d:\\crscu\\route_palnning\\codes\\Multi-agent-pathfinding-3d-LC-redis-v4

Total : 51 files,  11744 codes, 2321 comments, 2507 blanks, all 16572 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [PACKAGE\_GUIDE.md](/PACKAGE_GUIDE.md) | Markdown | 113 | 0 | 26 | 139 |
| [README.md](/README.md) | Markdown | 107 | 0 | 22 | 129 |
| [main.py](/main.py) | Python | 64 | 11 | 16 | 91 |
| [requirements.txt](/requirements.txt) | pip requirements | 8 | 0 | 1 | 9 |
| [src/\_\_init\_\_.py](/src/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/config/\_\_init\_\_.py](/src/config/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/config/settings.py](/src/config/settings.py) | Python | 154 | 12 | 38 | 204 |
| [src/core/\_\_init\_\_.py](/src/core/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/core/map/\_\_init\_\_.py](/src/core/map/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/core/map/cache/chongqing/grid\_mappings.json](/src/core/map/cache/chongqing/grid_mappings.json) | JSON | 18 | 0 | 0 | 18 |
| [src/core/map/cache/shaoquan/grid\_mappings.json](/src/core/map/cache/shaoquan/grid_mappings.json) | JSON | 1 | 0 | 0 | 1 |
| [src/core/map/grid\_converter.py](/src/core/map/grid_converter.py) | Python | 522 | 83 | 109 | 714 |
| [src/core/map/map\_handler\_3d.py](/src/core/map/map_handler_3d.py) | Python | 695 | 163 | 175 | 1,033 |
| [src/core/map/obstacle\_manager.py](/src/core/map/obstacle_manager.py) | Python | 157 | 10 | 38 | 205 |
| [src/core/map/occupancy\_map.py](/src/core/map/occupancy_map.py) | Python | 256 | 23 | 38 | 317 |
| [src/core/map/optimized\_polygon\_check.py](/src/core/map/optimized_polygon_check.py) | Python | 202 | 63 | 77 | 342 |
| [src/core/node\_3d.py](/src/core/node_3d.py) | Python | 130 | 7 | 28 | 165 |
| [src/core/pathfinding/\_\_init\_\_.py](/src/core/pathfinding/__init__.py) | Python | 1 | 0 | 1 | 2 |
| [src/core/pathfinding/astar.py](/src/core/pathfinding/astar.py) | Python | 238 | 24 | 48 | 310 |
| [src/core/pathfinding/astar\_v2.py](/src/core/pathfinding/astar_v2.py) | Python | 744 | 181 | 155 | 1,080 |
| [src/core/pathfinding/high\_level\_policy\_3d.py](/src/core/pathfinding/high_level_policy_3d.py) | Python | 168 | 48 | 41 | 257 |
| [src/core/pathfinding/jps.py](/src/core/pathfinding/jps.py) | Python | 788 | 172 | 155 | 1,115 |
| [src/core/pathfinding/jps\_v2.py](/src/core/pathfinding/jps_v2.py) | Python | 922 | 198 | 188 | 1,308 |
| [src/core/pathfinding/jps\_v2\_v1.py](/src/core/pathfinding/jps_v2_v1.py) | Python | 948 | 192 | 191 | 1,331 |
| [src/core/pathfinding/jps\_v3.py](/src/core/pathfinding/jps_v3.py) | Python | 972 | 208 | 197 | 1,377 |
| [src/core/pathfinding/jps\_v4.py](/src/core/pathfinding/jps_v4.py) | Python | 969 | 189 | 196 | 1,354 |
| [src/core/validation/\_\_init\_\_.py](/src/core/validation/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/core/validation/route\_validator.py](/src/core/validation/route_validator.py) | Python | 256 | 54 | 51 | 361 |
| [src/handlers/\_\_init\_\_.py](/src/handlers/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/handlers/kafka\_consumer.py](/src/handlers/kafka_consumer.py) | Python | 207 | 14 | 33 | 254 |
| [src/handlers/message\_handlers/\_\_init\_\_.py](/src/handlers/message_handlers/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/handlers/message\_handlers/approval\_handler.py](/src/handlers/message_handlers/approval_handler.py) | Python | 77 | 22 | 17 | 116 |
| [src/handlers/message\_handlers/base.py](/src/handlers/message_handlers/base.py) | Python | 378 | 38 | 63 | 479 |
| [src/handlers/message\_handlers/no\_fly\_zone\_handler.py](/src/handlers/message_handlers/no_fly_zone_handler.py) | Python | 182 | 74 | 38 | 294 |
| [src/handlers/message\_handlers/planning\_handler.py](/src/handlers/message_handlers/planning_handler.py) | Python | 188 | 53 | 36 | 277 |
| [src/handlers/message\_handlers/reroute\_handler.py](/src/handlers/message_handlers/reroute_handler.py) | Python | 131 | 33 | 34 | 198 |
| [src/handlers/message\_handlers/route\_handler.py](/src/handlers/message_handlers/route_handler.py) | Python | 111 | 20 | 18 | 149 |
| [src/handlers/message\_handlers/state\_handler.py](/src/handlers/message_handlers/state_handler.py) | Python | 38 | 5 | 13 | 56 |
| [src/handlers/risk\_assessment\_consumer.py](/src/handlers/risk_assessment_consumer.py) | Python | 460 | 72 | 84 | 616 |
| [src/tests/\_\_init\_\_.py](/src/tests/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/tests/fixed\_routes.json](/src/tests/fixed_routes.json) | JSON | 145 | 0 | 0 | 145 |
| [src/tests/test\_3d.py](/src/tests/test_3d.py) | Python | 416 | 147 | 132 | 695 |
| [src/tests/test\_producer.py](/src/tests/test_producer.py) | Python | 208 | 33 | 31 | 272 |
| [src/tests/visualization\_3d.py](/src/tests/visualization_3d.py) | Python | 287 | 53 | 58 | 398 |
| [src/utils/\_\_init\_\_.py](/src/utils/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/utils/exceptions.py](/src/utils/exceptions.py) | Python | 18 | 0 | 17 | 35 |
| [src/utils/logging.py](/src/utils/logging.py) | Python | 32 | 5 | 12 | 49 |
| [src/utils/visualization.py](/src/utils/visualization.py) | Python | 168 | 22 | 40 | 230 |
| [test\_complex\_polygon.py](/test_complex_polygon.py) | Python | 105 | 35 | 31 | 171 |
| [test\_polygon\_buffer.py](/test_polygon_buffer.py) | Python | 31 | 12 | 12 | 55 |
| [test\_polygon\_buffer\_simple.py](/test_polygon_buffer_simple.py) | Python | 129 | 45 | 38 | 212 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)