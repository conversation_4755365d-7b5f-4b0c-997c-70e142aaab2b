#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的浮点数坐标测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import math

# 创建一个模拟的地图类用于测试
class MockMap3D:
    def __init__(self):
        self.height = 1000
        self.width = 1000
        self.depth = 100
        
        # 模拟障碍物管理器
        self.obstacle_manager = MockObstacleManager()
        
        # 模拟网格转换器
        self.grid_converter = MockGridConverter()
        
        # 模拟不可通行点集合
        self.non_traversable = set()
    
    def traversable(self, y, x, z):
        return (y, x, z) not in self.non_traversable

class MockObstacleManager:
    def __init__(self):
        self.orbit_paths = {}
        self.orbit_quadrants = {}
    
    def get_type_at_position(self, pos):
        return []
    
    def get_orbit_path(self, zone_name):
        return []
    
    def get_orbit_quadrants(self, zone_name):
        return {}

class MockGridConverter:
    def relative_to_geo(self, y, x, z):
        return {"lat": 39.0 + y * 0.001, "lon": 116.0 + x * 0.001, "alt": z * 10}

def test_direct_move_function():
    """测试直线移动函数的浮点数支持"""
    print("测试直线移动函数的浮点数支持...")
    
    # 导入轨道路径规划器
    from src.core.pathfinding.orbit import OrbitPathFinder
    
    # 创建模拟地图
    mock_map = MockMap3D()
    
    # 创建轨道路径规划器
    orbit_finder = OrbitPathFinder(mock_map)
    
    # 测试用例
    test_cases = [
        {
            "name": "水平移动",
            "current_pos": (100.0, 100.0, 50.0),
            "goal": (100.0, 200.0, 50.0),
            "expected_direction": "正东"
        },
        {
            "name": "对角线移动",
            "current_pos": (100.0, 100.0, 50.0),
            "goal": (200.0, 200.0, 50.0),
            "expected_direction": "东南"
        },
        {
            "name": "精确角度移动",
            "current_pos": (100.0, 100.0, 50.0),
            "goal": (150.0, 300.0, 50.0),
            "expected_direction": "自定义角度"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试 {test_case['name']}:")
        print(f"  起点: {test_case['current_pos']}")
        print(f"  终点: {test_case['goal']}")
        
        try:
            # 调用直线移动函数
            next_pos, next_time, obstacle_zone = orbit_finder._direct_move_towards_goal(
                test_case["current_pos"],
                test_case["goal"],
                0,  # current_time
                40,  # min_height
                "test",  # agent_id
                None,  # constraints
                None,  # occupancy_map
                1.0  # step_size
            )
            
            if next_pos:
                print(f"  ✓ 移动成功")
                print(f"  下一个位置: ({next_pos[0]:.3f}, {next_pos[1]:.3f}, {next_pos[2]:.3f})")
                
                # 计算移动方向
                dy = next_pos[0] - test_case["current_pos"][0]
                dx = next_pos[1] - test_case["current_pos"][1]
                distance = math.sqrt(dy**2 + dx**2)
                
                if distance > 0:
                    angle = math.degrees(math.atan2(dy, dx))
                    print(f"  移动角度: {angle:.2f}°")
                    print(f"  移动距离: {distance:.3f}")
                    
                    # 验证是否朝向目标
                    goal_dy = test_case["goal"][0] - test_case["current_pos"][0]
                    goal_dx = test_case["goal"][1] - test_case["current_pos"][1]
                    goal_angle = math.degrees(math.atan2(goal_dy, goal_dx))
                    
                    angle_diff = abs(angle - goal_angle)
                    if angle_diff > 180:
                        angle_diff = 360 - angle_diff
                    
                    if angle_diff < 1.0:  # 1度容差
                        print(f"  ✓ 移动方向正确 (误差: {angle_diff:.3f}°)")
                    else:
                        print(f"  ✗ 移动方向偏差较大 (误差: {angle_diff:.3f}°)")
                
            else:
                print(f"  ✗ 移动失败: {obstacle_zone}")
                
        except Exception as e:
            print(f"  ✗ 测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_path_smoothness():
    """测试路径平滑度"""
    print("\n\n测试路径平滑度...")
    
    # 导入轨道路径规划器
    from src.core.pathfinding.orbit import OrbitPathFinder
    
    # 创建模拟地图
    mock_map = MockMap3D()
    
    # 创建轨道路径规划器
    orbit_finder = OrbitPathFinder(mock_map)
    
    # 模拟一条简单的直线路径
    start = (100, 100, 50)
    goal = (200, 300, 50)
    
    print(f"测试路径: {start} -> {goal}")
    
    try:
        # 执行路径规划
        path, error = orbit_finder.find_path(
            start=start,
            goal=goal,
            min_height=50,
            agent_id="test_drone",
            start_time=0
        )
        
        if path:
            print(f"✓ 路径规划成功，路径长度: {len(path)} 个节点")
            
            # 分析路径点的分布
            print("路径点分析:")
            for i, node in enumerate(path[:10]):  # 只显示前10个点
                print(f"  节点 {i}: ({node.y}, {node.x}, {node.z}) at t={node.t}")
            
            if len(path) > 10:
                print(f"  ... (省略 {len(path) - 10} 个节点)")
            
            # 计算路径的直线度
            if len(path) >= 2:
                start_node = path[0]
                end_node = path[-1]
                
                direct_distance = math.sqrt(
                    (end_node.y - start_node.y)**2 + 
                    (end_node.x - start_node.x)**2
                )
                
                actual_distance = 0
                for i in range(1, len(path)):
                    actual_distance += math.sqrt(
                        (path[i].y - path[i-1].y)**2 + 
                        (path[i].x - path[i-1].x)**2
                    )
                
                efficiency = (direct_distance / actual_distance) * 100 if actual_distance > 0 else 0
                
                print(f"路径效率分析:")
                print(f"  直线距离: {direct_distance:.2f}")
                print(f"  实际距离: {actual_distance:.2f}")
                print(f"  路径效率: {efficiency:.1f}%")
                
                if efficiency > 95:
                    print(f"  ✓ 路径非常平滑")
                elif efficiency > 85:
                    print(f"  ✓ 路径较为平滑")
                else:
                    print(f"  ⚠ 路径可能需要优化")
        else:
            print(f"✗ 路径规划失败: {error}")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("=" * 50)
    print("浮点数坐标功能测试")
    print("=" * 50)
    
    # 测试直线移动函数
    test_direct_move_function()
    
    # 测试路径平滑度
    test_path_smoothness()
    
    print("\n" + "=" * 50)
    print("浮点数坐标功能测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
