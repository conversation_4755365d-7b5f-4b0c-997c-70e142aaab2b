#include "occupancy_map.h"
#include <algorithm> // For std::sort, std::min, std::max, std::remove_if
#include <cmath>     // For std::round, std::abs
#include <vector>
#include <map> // For std::map in add_path temporarily for new_intervals_for_path
// #include <iostream> // No longer needed here, moved to test_occupancy_map.cpp
// #include <memory>   // No longer needed here

// Definition of static member variables
std::array<int, 3> OccupancyMap::map_dimensions_ = {0, 0, 0};
int OccupancyMap::map_height_ = 0;
int OccupancyMap::map_width_ = 0;
int OccupancyMap::map_depth_ = 0;
int OccupancyMap::time_buffer_ = -1;
std::unordered_map<std::tuple<int, int, int>, std::vector<TimeInterval>> OccupancyMap::occupancy_data_;
std::unordered_map<std::string, std::unordered_set<std::tuple<int, int, int>>> OccupancyMap::agent_spatial_occupancy_;

OccupancyMap::OccupancyMap(const std::array<int, 3> &map_size_param, int time_buffer_param, int safety_offsets_param)
    : safety_offsets_val_(safety_offsets_param)
{
    if (time_buffer_ == -1)
    {
        map_dimensions_ = map_size_param;
        map_height_ = map_size_param[0];
        map_width_ = map_size_param[1];
        map_depth_ = map_size_param[2];
        time_buffer_ = time_buffer_param;
    }
}

std::vector<TimeInterval> OccupancyMap::_merge_intervals(std::vector<TimeInterval> &intervals) const
{
    if (intervals.empty())
    {
        return {};
    }

    std::sort(intervals.begin(), intervals.end(), [](const TimeInterval &a, const TimeInterval &b)
              {
        if (a.agent_id != b.agent_id) {
            return a.agent_id < b.agent_id;
        }
        return a.start < b.start; });

    std::vector<TimeInterval> merged;
    if (intervals.empty())
        return merged;

    merged.push_back(intervals[0]);

    for (size_t i = 1; i < intervals.size(); ++i)
    {
        TimeInterval &current_merged = merged.back();
        const TimeInterval &next_interval = intervals[i];

        if (current_merged.agent_id == next_interval.agent_id)
        {
            std::optional<TimeInterval> merged_interval = current_merged.merge(next_interval);
            if (merged_interval)
            {
                current_merged = *merged_interval;
            }
            else
            {
                merged.push_back(next_interval);
            }
        }
        else
        {
            merged.push_back(next_interval);
        }
    }
    return merged;
}

// Modified to use GridNodeData
void OccupancyMap::add_path(const std::vector<GridNodeData> &path, const std::string &agent_id)
{
    if (path.empty())
        return;

    std::vector<int> deltas;
    if (safety_offsets_val_ == 0)
    {
        deltas = {0};
    }
    else
    {
        deltas = {-safety_offsets_val_, 0, safety_offsets_val_};
    }

    std::unordered_map<std::tuple<int, int, int>, std::vector<TimeInterval>> new_intervals_for_path;
    std::unordered_set<std::tuple<int, int, int>> affected_positions_this_path;

    for (const auto &node : path) // Changed from node_ptr to node (GridNodeData by value/ref)
    {
        // const GridNodeData &node = *node_ptr; // No longer needed
        std::tuple<int, int, int> central_pos = {
            static_cast<int>(std::round(node.y)),
            static_cast<int>(std::round(node.x)),
            static_cast<int>(std::round(node.z))};
        int t = static_cast<int>(std::round(node.t));

        for (int dy : deltas)
        {
            for (int dx : deltas)
            {
                for (int dz : deltas)
                {
                    int y = std::get<0>(central_pos) + dy;
                    int x = std::get<1>(central_pos) + dx;
                    int z = std::get<2>(central_pos) + dz;

                    if (y >= 0 && y < map_height_ &&
                        x >= 0 && x < map_width_ &&
                        z >= 0 && z < map_depth_)
                    {
                        std::tuple<int, int, int> occupancy_pos = {y, x, z};
                        agent_spatial_occupancy_[agent_id].insert(occupancy_pos);

                        TimeInterval interval(t - time_buffer_, t + time_buffer_, agent_id);
                        new_intervals_for_path[occupancy_pos].push_back(interval);
                        affected_positions_this_path.insert(occupancy_pos);
                    }
                }
            }
        }
    }

    for (const auto &occupancy_pos : affected_positions_this_path)
    {
        if (new_intervals_for_path.count(occupancy_pos))
        {
            occupancy_data_[occupancy_pos].insert(
                occupancy_data_[occupancy_pos].end(),
                new_intervals_for_path[occupancy_pos].begin(),
                new_intervals_for_path[occupancy_pos].end());
        }
        if (occupancy_data_.count(occupancy_pos) && occupancy_data_[occupancy_pos].size() > 10)
        {
            occupancy_data_[occupancy_pos] = _merge_intervals(occupancy_data_[occupancy_pos]);
        }
    }
}

std::tuple<bool, std::optional<std::string>> OccupancyMap::check_collision(const std::tuple<int, int, int> &pos, int t) const
{
    int y = std::get<0>(pos);
    int x = std::get<1>(pos);
    int z = std::get<2>(pos);

    if (!(y >= 0 && y < map_height_ &&
          x >= 0 && x < map_width_ &&
          z >= 0 && z < map_depth_))
    {
        return {true, std::nullopt};
    }

    if (occupancy_data_.count(pos))
    {
        for (const auto &interval : occupancy_data_.at(pos))
        {
            if (interval.start <= t && t <= interval.end)
            {
                return {true, interval.agent_id};
            }
        }
    }
    return {false, std::nullopt};
}

std::optional<std::tuple<int, int, int, int, std::string, std::string>>
OccupancyMap::get_conflict(const std::tuple<int, int, int> &pos1, const std::tuple<int, int, int> &pos2, int t) const
{
    if (std::abs(std::get<0>(pos1) - std::get<0>(pos2)) <= 1 &&
        std::abs(std::get<1>(pos1) - std::get<1>(pos2)) <= 1 &&
        std::abs(std::get<2>(pos1) - std::get<2>(pos2)) <= 1)
    {
        auto [collision1, agent1_opt] = check_collision(pos1, t);
        auto [collision2, agent2_opt] = check_collision(pos2, t);

        if (collision1 && collision2 && agent1_opt && agent2_opt && *agent1_opt != *agent2_opt)
        {
            return std::make_tuple(std::get<0>(pos1), std::get<1>(pos1), std::get<2>(pos1), t, *agent1_opt, *agent2_opt);
        }
    }
    return std::nullopt;
}

std::unordered_set<std::string> OccupancyMap::get_occupying_agents(const std::tuple<int, int, int> &pos, int t) const
{
    std::unordered_set<std::string> agents;
    if (occupancy_data_.count(pos))
    {
        for (const auto &interval : occupancy_data_.at(pos))
        {
            if (interval.start <= t && t <= interval.end)
            {
                agents.insert(interval.agent_id);
            }
        }
    }
    return agents;
}

void OccupancyMap::clear()
{
    occupancy_data_.clear();
    agent_spatial_occupancy_.clear();
}

void OccupancyMap::remove_agent(const std::string &agent_id)
{
    if (agent_spatial_occupancy_.count(agent_id))
    {
        const auto &occupied_positions_by_agent = agent_spatial_occupancy_.at(agent_id);
        for (const auto &pos : occupied_positions_by_agent)
        {
            if (occupancy_data_.count(pos))
            {
                auto &intervals_at_pos = occupancy_data_.at(pos);
                intervals_at_pos.erase(
                    std::remove_if(intervals_at_pos.begin(), intervals_at_pos.end(),
                                   [&](const TimeInterval &interval)
                                   {
                                       return interval.agent_id == agent_id;
                                   }),
                    intervals_at_pos.end());

                if (intervals_at_pos.empty())
                {
                    occupancy_data_.erase(pos);
                }
            }
        }
        agent_spatial_occupancy_.erase(agent_id);
    }
}

// Modified to use GridNodeData
std::tuple<std::optional<int>, bool> OccupancyMap::find_valid_time(
    const std::vector<GridNodeData> &path, // Changed from std::vector<std::shared_ptr<GridNode>>
    const std::string &agent_id,
    int path_original_start_time,
    int time_direction,
    int max_offset) const
{
    if (path.empty())
    {
        return {std::nullopt, false};
    }

    int current_abs_time = path_original_start_time;
    std::unordered_set<int> tried_times;

    while (std::abs(current_abs_time - path_original_start_time) <= max_offset)
    {
        tried_times.insert(current_abs_time);

        bool conflict_found_this_iteration = false;
        std::vector<std::pair<TimeInterval, int>> conflicts;

        for (const auto &node : path) // Changed from node_ptr to node
        {
            // const GridNodeData &node = *node_ptr; // No longer needed
            int relative_t = static_cast<int>(std::round(node.t)) - path_original_start_time;
            int actual_t = current_abs_time + relative_t;

            if (actual_t < 0)
            {
                conflicts.emplace_back(TimeInterval(0, 0, "INVALID_TIME"), relative_t);
                conflict_found_this_iteration = true;
            }

            std::tuple<int, int, int> node_pos = {
                static_cast<int>(std::round(node.y)),
                static_cast<int>(std::round(node.x)),
                static_cast<int>(std::round(node.z))};

            if (occupancy_data_.count(node_pos))
            {
                for (const auto &interval : occupancy_data_.at(node_pos))
                {
                    if (interval.agent_id != agent_id && interval.start <= actual_t && actual_t <= interval.end)
                    {
                        conflicts.emplace_back(interval, relative_t);
                        conflict_found_this_iteration = true;
                    }
                }
            }
        }

        if (!conflict_found_this_iteration)
        {
            return {current_abs_time, current_abs_time != path_original_start_time};
        }

        int forward_offset_needed = 0;
        int backward_offset_needed = 0;

        if (time_direction >= 0)
        {
            int max_push = 0;
            for (const auto &conflict_pair : conflicts)
            {
                const TimeInterval &interval = conflict_pair.first;
                int node_relative_t = conflict_pair.second;
                if (interval.agent_id == "INVALID_TIME")
                {
                    max_push = std::max(max_push, -(current_abs_time + node_relative_t));
                }
                else
                {
                    max_push = std::max(max_push, interval.end + 1 - (current_abs_time + node_relative_t));
                }
            }
            forward_offset_needed = max_push > 0 ? max_push : 1;
        }

        if (time_direction <= 0)
        {
            int min_pull = 0;
            bool first_valid_pull = true;
            for (const auto &conflict_pair : conflicts)
            {
                const TimeInterval &interval = conflict_pair.first;
                int node_relative_t = conflict_pair.second;
                if (interval.agent_id == "INVALID_TIME")
                    continue;

                int pull_for_this_conflict = interval.start - 1 - (current_abs_time + node_relative_t);
                if (first_valid_pull)
                {
                    min_pull = pull_for_this_conflict;
                    first_valid_pull = false;
                }
                else
                {
                    min_pull = std::max(min_pull, pull_for_this_conflict);
                }
            }
            backward_offset_needed = min_pull < 0 ? min_pull : -1;
        }

        if (time_direction > 0)
        {
            current_abs_time += forward_offset_needed;
        }
        else if (time_direction < 0)
        {
            int potential_new_time = current_abs_time + backward_offset_needed;
            if (potential_new_time < 0 && time_direction == 0)
            {
                current_abs_time += forward_offset_needed;
            }
            else if (potential_new_time < 0 && time_direction < 0)
            {
                return {std::nullopt, false};
            }
            else
            {
                current_abs_time += backward_offset_needed;
            }
        }
        else
        {
            bool can_go_backward_safely = (current_abs_time + backward_offset_needed >= 0);

            if (can_go_backward_safely && backward_offset_needed != 0 && conflicts.size() > 0 &&
                (forward_offset_needed == 0 || std::abs(backward_offset_needed) < std::abs(forward_offset_needed)))
            {
                current_abs_time += backward_offset_needed;
            }
            else if (forward_offset_needed != 0 && conflicts.size() > 0)
            {
                current_abs_time += forward_offset_needed;
            }
            else if (conflicts.empty())
            {
                return {std::nullopt, false};
            }
            else
            {
                current_abs_time += forward_offset_needed;
            }
        }
        if (forward_offset_needed == 0 && backward_offset_needed == 0 && conflict_found_this_iteration)
        {
            return {std::nullopt, false};
        }
    }
    return {std::nullopt, false};
}

OccupancyMap OccupancyMap::copy() const
{
    return OccupancyMap(map_dimensions_, time_buffer_, this->safety_offsets_val_);
}

// Test main function has been moved to cpp_implementation/src/tests/test_occupancy_map.cpp
