#ifndef GEOMETRY_UTILS_H
#define GEOMETRY_UTILS_H

#include <vector>
#include <cmath>
#include <limits>
#include <algorithm> // For std::min, std::max

#include "geometry_types.h" // Contains Point2D, Polygon2D

// It's good practice to define M_PI if not already defined
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace CVToolbox
{
    namespace GeometryUtils
    {

        // --- Point and Vector Operations ---
        // (Some basic operations are in Point2D struct itself, more complex ones can go here)
        inline double distance_sq(const Point2D &p1, const Point2D &p2)
        {
            double dy = p1.y - p2.y;
            double dx = p1.x - p2.x;
            return dy * dy + dx * dx;
        }

        inline double distance(const Point2D &p1, const Point2D &p2)
        {
            return std::sqrt(distance_sq(p1, p2));
        }

        // --- Polygon Operations ---

        /**
         * @brief Checks if a point is inside a polygon using the Ray Casting algorithm.
         * Handles convex and non-convex polygons.
         * Points on the boundary are typically considered inside.
         * @param point The point to check.
         * @param polygon The vertices of the polygon, ordered sequentially.
         * @return True if the point is inside or on the boundary of the polygon, false otherwise.
         */
        bool is_point_in_polygon_precise(const Point2D &point, const Polygon2D &polygon);

        /**
         * @brief Checks a batch of points if they are inside a given polygon.
         * @param points Vector of points to check.
         * @param polygon The polygon vertices.
         * @return Vector of booleans, true if corresponding point is inside.
         */
        std::vector<bool> points_in_polygon_vectorized(const std::vector<Point2D> &points, const Polygon2D &polygon);

        /**
         * @brief Expands a polygon outwards by a specified distance.
         * This is a complex operation and the Python version's logic needs careful translation.
         * The quality of expansion can depend heavily on the algorithm used, especially for
         * concave polygons or polygons with very sharp angles.
         * @param polygon The original polygon vertices.
         * @param expand_distance The distance to expand outwards.
         * @return A new polygon with expanded vertices.
         */
        Polygon2D expand_polygon(const Polygon2D &polygon, double expand_distance);

        // --- Line Segment Operations ---
        /**
         * @brief Helper function to determine orientation of an ordered triplet (p, q, r).
         * @return 0 if p, q, r are collinear
         *         1 if clockwise
         *         2 if counterclockwise
         */
        int orientation(const Point2D &p, const Point2D &q, const Point2D &r);

        /**
         * @brief Given three collinear points p, q, r, the function checks if
         * point q lies on line segment 'pr'.
         */
        bool on_segment(const Point2D &p, const Point2D &q, const Point2D &r);

        /**
         * @brief Checks if line segment 'p1q1' and 'p2q2' intersect.
         */
        bool segments_intersect(const Point2D &p1, const Point2D &q1, const Point2D &p2, const Point2D &q2);

    } // namespace GeometryUtils
} // namespace CVToolbox
#endif // GEOMETRY_UTILS_H
