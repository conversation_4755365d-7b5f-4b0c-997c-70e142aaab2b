from typing import Dict, List, Optional, <PERSON>ple
import time
import math
from ...utils.logging import get_logger
from .base import MessageHandler
from concurrent.futures import ThreadPoolExecutor
from ...core.node_3d import GridNode3D

logger = get_logger(__name__)


class RerouteMessageHandler(MessageHandler):
    """处理飞行中改航消息"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 创建线程池用于异步数据库操作
        self.executor = ThreadPoolExecutor(max_workers=2)

    def handle_message(self, message: Dict) -> Tuple[Optional[List], Optional[str]]:
        """
        处理改航消息

        Args:
            message: 消息字典，包含当前位置和目标位置信息

        Returns:
            Tuple[Optional[List], Optional[str]]: (新路径点列表, 错误信息)
        """
        try:
            # visualizer = Visualizer3D(self.map)
            # visualizer.plot_obstacles()

            flight_id = message["flight_id"]

            # 起点海拔高度
            takeoff_alt = float(
                self.planned_paths[flight_id]["data"]["takeOffPoint"].split(",")[-1]
            )

            start_point = {
                "lat": message["posPre"][0],
                "lon": message["posPre"][1],
                "alt": message["posPre"][2] + takeoff_alt,
            }
            end_point = self.parse_point(
                self.planned_paths[flight_id]["data"]["landingPoint"]
            )
            # end_point = {
            #     "lat": self.planned_paths[flight_id]["data"]["landingPoint"][1],
            #     "lon": self.planned_paths[flight_id]["data"]["landingPoint"][0],
            #     "alt": self.planned_paths[flight_id]["data"]["landingPoint"][2]
            #     + takeoff_alt,
            # }
            start_index = message["posFinal"][1]
            flightheight = self.planned_paths[flight_id]["data"]["flyHeight"]

            # 从占用图中删除旧路径
            self.occupancy_map.remove_agent(flight_id)

            # 重新规划路径
            path_nodes, turn_path_nodes = self._replan_path(
                flight_id,
                start_point,
                end_point,
                flightheight,  # 最小巡航高度，可以从配置中读取
                int(time.time()),
                start_index,
            )

            if not path_nodes:
                raise Exception("路径规划失败")

            turn_path_geo = []
            base_height = 0
            for i, node in enumerate(turn_path_nodes):
                coords = self.grid_converter.relative_to_geo(node.y, node.x, node.z)
                turn_path_geo.append(
                    {
                        "index": i,
                        "lng": coords["lon"],
                        "lat": coords["lat"],
                        "height": coords["alt"] - base_height,
                        "time": node.t,  # 添加时间信息
                    }
                )
                base_height = takeoff_alt

            # 直接发给无人机（贾博仿真无人机）
            response_data = {
                "flightapplyid": flight_id,
                "device_sn": message["sn"],
                "planned_path_points": turn_path_geo[1:],
                "changeroute": True,
                "reason": "重新规划路径（禁飞区冲突）",
            }
            self._send_response(
                request=message,
                new_data=response_data,
                response_topic=self.uav_topic,
            )

            # 保存告警点索引
            # message["alarm_index"] = start_index

            # 存储数据库
            route_id = message["uavroot_id"]
            route_name = f"自动规划路径{route_id}"
            # 计算预计时间（秒）
            estimated_time = int(turn_path_geo[-1]["time"] - turn_path_geo[0]["time"])
            # 计算总距离（点数 * 5米）
            all_distance = len(path_nodes) * 5

            # 异步执行数据库操作
            # self.executor.submit(
            #     self._save_to_db,
            #     route_id,
            #     route_name,
            #     estimated_time,
            #     all_distance,
            #     path,
            #     True,
            # )

            self._save_to_db(
                route_id,
                route_name,
                estimated_time,
                all_distance,
                turn_path_geo[1:],
                True,
            )

            # 存储新路径
            self.store_path(flight_id, path_nodes, turn_path_nodes, None)

            # 添加到占用图，使用带有时间信息的节点
            self.occupancy_map.add_path(path_nodes, flight_id)

            # 可视化路径
            # self.visualize_paths()

            return turn_path_geo, None

        except Exception as e:
            error_msg = f"处理改航消息时出错: {str(e)}"
            logger.error(error_msg)
            return None, error_msg

    def _replan_path(
        self,
        flight_id: str,
        start_point: Dict,
        end_point: Dict,
        min_cruise_alt: float,
        begin_time: int,
        start_index: int,
    ):
        """重新规划路径"""
        # 获取原始路径
        if flight_id in self.planned_paths:
            pre_turn_path_nodes = self.planned_paths[flight_id]["turn_path_nodes"][
                : start_index + 1
            ]

        # 计算相对高度
        relative_min_cruise_alt = self.grid_converter.height_to_relative(min_cruise_alt)

        # 转换成网格坐标
        start_grid = self.grid_converter.geo_to_relative(
            start_point["lat"],
            start_point["lon"],
            start_point["alt"],
        )
        end_grid = self.grid_converter.geo_to_relative(
            end_point["lat"],
            end_point["lon"],
            end_point["alt"],
        )

        # 规划路线
        # from ...core.pathfinding.high_level_policy_3d import CBS3D

        # planner = CBS3D(self.map, self.occupancy_map)
        solution, error = self.planner.solve(
            # solution, error = planner.solve(
            [start_grid],
            [end_grid],
            relative_min_cruise_alt,
            agent_ids=[flight_id],
            start_times=[begin_time],
        )

        if not solution:
            raise Exception(f"路径规划失败: {error}")

        turn_path_nodes = solution[flight_id][1]  # node

        complete_turn_path_nodes = pre_turn_path_nodes + turn_path_nodes[1:]

        complete_path_nodes = self._interpolate_nodes(turn_path_nodes, 5)

        return complete_path_nodes, complete_turn_path_nodes

    def _interpolate_nodes(
        self, nodes: List["GridNode3D"], interval: int = 5
    ) -> List["GridNode3D"]:
        """在路径节点中每两个相邻节点之间进行插值

        每隔指定的网格距离插入一个新的节点，包括坐标和时间插值

        Args:
            nodes: 原始路径节点列表 [GridNode3D, ...]
            interval: 插值间隔，每隔多少个网格插入一个点，默认为5

        Returns:
            List[GridNode3D]: 插值后的路径节点列表
        """

        if not nodes or len(nodes) < 2:
            return nodes.copy() if nodes else []

        interpolated_nodes = [nodes[0]]  # 先添加起始节点

        # 处理每对相邻的节点
        for i in range(len(nodes) - 1):
            n1 = nodes[i]
            n2 = nodes[i + 1]

            # 计算方向向量
            dy = n2.y - n1.y
            dx = n2.x - n1.x
            dz = n2.z - n1.z
            dt = n2.t - n1.t  # 时间差

            # 计算两点之间的欧几里得距离
            distance = math.sqrt(dy**2 + dx**2 + dz**2)

            # 如果距离小于间隔，不需要插值
            if distance < interval:
                continue

            # 计算插值点数量
            num_points = int(distance / interval)

            # 生成插值点
            for j in range(1, num_points + 1):
                ratio = j * interval / distance
                if ratio >= 1:  # 避免超过终点
                    break

                # 计算插值点坐标和时间
                y = int(n1.y + dy * ratio)
                x = int(n1.x + dx * ratio)
                z = int(n1.z + dz * ratio)
                t = int(n1.t + dt * ratio)  # 插值时间

                # 创建新节点
                new_node = GridNode3D(y, x, z, t)
                interpolated_nodes.append(new_node)

            # 添加终点（除了最后一对点的终点外）
            if i < len(nodes) - 2:
                interpolated_nodes.append(n2)

        # 添加最后一个节点
        interpolated_nodes.append(nodes[-1])

        return interpolated_nodes

    def _interpolate_grid_path(
        self, grid_path: List[Tuple[int, int, int]], interval: int = 5
    ) -> List[Tuple[int, int, int]]:
        """在网格路径中每两个相邻点之间进行插值

        每隔指定的网格距离插入一个新的点，以增加路径的密度

        Args:
            grid_path: 原始网格路径点列表 [(y, x, z), ...]
            interval: 插值间隔，每隔多少个网格插入一个点，默认为5

        Returns:
            List[Tuple[int, int, int]]: 插值后的网格路径点列表
        """
        if not grid_path or len(grid_path) < 2:
            return grid_path.copy() if grid_path else []

        interpolated_path = [grid_path[0]]  # 先添加起始点

        # 处理每对相邻的点
        for i in range(len(grid_path) - 1):
            p1 = grid_path[i]
            p2 = grid_path[i + 1]

            # 计算方向向量
            dy = p2[0] - p1[0]
            dx = p2[1] - p1[1]
            dz = p2[2] - p1[2]

            # 计算两点之间的欧几里得距离
            distance = math.sqrt(dy**2 + dx**2 + dz**2)

            # 如果距离小于间隔，不需要插值
            if distance < interval:
                continue

            # 计算插值点数量
            num_points = int(distance / interval)

            # 生成插值点
            for j in range(1, num_points + 1):
                ratio = j * interval / distance
                if ratio >= 1:  # 避免超过终点
                    break

                # 计算插值点坐标
                y = int(p1[0] + dy * ratio)
                x = int(p1[1] + dx * ratio)
                z = int(p1[2] + dz * ratio)

                interpolated_path.append((y, x, z))

            # 添加终点（除了最后一对点的终点外）
            if i < len(grid_path) - 2:
                interpolated_path.append(p2)

        # 添加最后一个点
        interpolated_path.append(grid_path[-1])

        return interpolated_path
