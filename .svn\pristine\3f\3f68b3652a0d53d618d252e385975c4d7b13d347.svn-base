# 部署服务器命令
以下是 `route_planning.service` 服务的关键命令总结：

---

### **1. 创建服务文件**
创建并编辑服务文件：
```bash
sudo vim /etc/systemd/system/route_planning.service
```

**服务文件内容模板：**
```ini
[Unit]
Description=Route Planning Background Service
After=network.target

[Service]
WorkingDirectory=/home/<USER>/routeplan/deploy
# 基本启动命令（使用工作目录中的config.json）
ExecStart=/home/<USER>/routeplan/deploy/route_planning_service

# 或者指定地点和服务器地址
# ExecStart=/home/<USER>/routeplan/deploy/route_planning_service --location shijiazhuang --server ***********

# 或者指定配置文件路径
# ExecStart=/home/<USER>/routeplan/deploy/route_planning_service --config /home/<USER>/routeplan/custom_config.json

# 或者同时指定配置文件路径、地点和服务器地址
# ExecStart=/home/<USER>/routeplan/deploy/route_planning_service --config /home/<USER>/routeplan/custom_config.json --location shijiazhuang --server ***********

Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

**注意：** 请根据实际需求选择上述ExecStart命令中的一种，并删除其他注释行。

---

### **2. 配置权限**
确保程序文件和目录有正确的权限：
```bash
chmod +x /home/<USER>/routeplan/deploy/route_planning_service
chmod 755 /home/<USER>/routeplan/deploy
```

---

### **3. 重新加载 systemd 配置**
每次创建或修改服务文件后，重新加载 systemd 配置：
```bash
sudo systemctl daemon-reload
```

---

### **4. 启动服务**
启动服务：
```bash
sudo systemctl start route_planning.service
```

---

### **5. 检查服务状态**
查看服务是否运行正常：
```bash
sudo systemctl status route_planning.service
```

---

### **6. 设置开机自启**
让服务开机自启：
```bash
sudo systemctl enable route_planning.service
```

---

### **7. 查看服务日志**
查看历史日志：
```bash
sudo journalctl -u route_planning.service
```

查看实时日志：
```bash
sudo journalctl -u route_planning.service -f
```

---

### **8. 停止或重启服务**
停止服务：
```bash
sudo systemctl stop route_planning.service
```

重启服务：
```bash
sudo systemctl restart route_planning.service
```

---

### **9. 禁用开机自启**
如果不需要服务开机自启：
```bash
sudo systemctl disable route_planning.service
```

---

### **10. 命令行参数说明**

路径规划服务支持以下命令行参数，可以在 `ExecStart` 中使用：

1. **--location**: 指定地点（例如：nanjing, shijiazhuang）
   ```bash
   ./route_planning_service --location shijiazhuang
   ```

2. **--server**: 指定服务器地址
   ```bash
   ./route_planning_service --server ***********
   ```

3. **--config**: 指定配置文件路径
   ```bash
   ./route_planning_service --config /path/to/your/config.json
   ```

4. **--mode**: 指定运行模式（normal 或 risk，默认为 risk）
   ```bash
   ./route_planning_service --mode normal
   ```

这些参数可以组合使用，例如：
```bash
./route_planning_service --config /path/to/your/config.json --location shijiazhuang --server ***********
```

**注意：**
- 命令行参数的优先级高于配置文件中的设置
- 如果不提供参数，程序会尝试从工作目录中加载 `config.json` 文件

---

通过这些命令，你可以完整地管理 `route_planning.service` 服务。如果有其他需要，随时告诉我！