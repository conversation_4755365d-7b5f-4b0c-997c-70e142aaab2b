# -*- coding: utf-8 -*-
import numpy as np
import math
from typing import List, Set, Dict, Tuple, Optional
from ..node_3d import GridNode3D
from ...config import settings
from ...utils.logging import get_logger

logger = get_logger(__name__)


class OrbitPathFinder:
    """基于轨道的路径规划器

    实现了垂直起飞、基于轨道的巡航和垂直降落三个阶段的路径规划。
    巡航阶段使用轨道避障策略，当遇到禁飞区时进入轨道移动。
    """

    def __init__(self, map3d):
        """初始化轨道路径规划器

        Args:
            map3d: 3D地图对象，包含环境信息
        """
        self.map = map3d
        self.height = map3d.height
        self.width = map3d.width
        self.depth = map3d.depth

        # 从配置获取速度参数，如果配置不可用则使用默认值
        try:
            self.takeoff_speed_t = 1 / settings.settings.pathplanning.takeoff_speed
            self.cruise_speed_t = 1 / settings.settings.pathplanning.cruise_speed
            self.landing_speed_t = 1 / settings.settings.pathplanning.landing_speed
        except (AttributeError, TypeError):
            # 使用默认值
            self.takeoff_speed_t = 1.0  # 默认起飞速度时间间隔
            self.cruise_speed_t = 1.0  # 默认巡航速度时间间隔
            self.landing_speed_t = 1.0  # 默认降落速度时间间隔

        # 轨道搜索参数
        self.orbit_search_radius = 10  # 搜索轨道点的范围
        self.max_orbit_iterations = 100  # 最大轨道迭代次数
        self.orbit_exit_check_distance = 5  # 检查退出轨道的距离

        # 防止死循环的参数
        self.max_orbit_switches = 3  # 最大轨道切换次数
        self.visited_zones = set()  # 记录访问过的禁飞区

    def _is_valid_position(
        self,
        pos: Tuple[int, int, int],
        t: int,
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
        occupancy_map,
        ignore_min_height: bool = False,
    ) -> Tuple[bool, Optional[str]]:
        """检查位置有效性，与jps_v4保持一致的接口"""
        # 检查地图边界
        if (
            pos[0] >= self.map.height
            or pos[1] >= self.map.width
            or pos[2] >= self.map.depth
            or pos[0] < 0
            or pos[1] < 0
            or pos[2] < 0
        ):
            real_pos = self.map.grid_converter.relative_to_geo(pos[0], pos[1], pos[2])
            return False, f"位置 {real_pos} 超出地图范围"

        # 检查位置是否可通行
        if not self.map.traversable(pos[0], pos[1], pos[2]):
            obstacle_types = self.map.obstacle_manager.get_type_at_position(pos)
            real_pos = self.map.grid_converter.relative_to_geo(pos[0], pos[1], pos[2])
            return False, f"位置 {real_pos} 与障碍物类型 {obstacle_types} 发生碰撞"

        # 检查最小高度约束
        if not ignore_min_height and pos[2] < min_height:
            real_alt = self.map.grid_converter.relative_to_geo(0, 0, pos[2])["alt"]
            real_min_alt = self.map.grid_converter.relative_to_geo(0, 0, min_height)[
                "alt"
            ]
            return False, f"飞行高度 {real_alt} 低于最小巡航高度 {real_min_alt}"

        # 检查约束和碰撞
        if constraints or occupancy_map:
            return self._check_constraints_and_collisions(
                pos, t, agent_id, constraints, occupancy_map
            )

        return True, None

    def _check_constraints_and_collisions(
        self, pos, t, agent_id, constraints, occupancy_map
    ):
        """检查约束和碰撞，与jps_v4保持一致"""
        # 检查与其他无人机的碰撞
        if occupancy_map:
            has_collision, colliding_agent = occupancy_map.check_collision(pos, t)
            if has_collision and colliding_agent != agent_id:
                return False, f"与无人机 {colliding_agent} 在时刻 {t} 发生碰撞"

        # 检查时空约束条件
        if constraints:
            for constraint in constraints:
                c_type, c_data = constraint
                if c_type == "v":  # 顶点约束
                    c_agent, c_y, c_x, c_z, c_t = c_data
                    if (
                        agent_id == c_agent
                        and pos[0] == c_y
                        and pos[1] == c_x
                        and pos[2] == c_z
                        and t == c_t
                    ):
                        return False, f"违反约束 {constraint}"

        return True, None

    def _get_obstacle_at_position(self, pos: Tuple[int, int, int]) -> Optional[str]:
        """获取位置处的禁飞区名称"""
        obstacle_types = self.map.obstacle_manager.get_type_at_position(pos)
        # 返回第一个禁飞区类型（假设一个位置只属于一个禁飞区）
        return obstacle_types[0] if obstacle_types else None

    def _find_nearby_orbit_points(
        self, pos: Tuple[int, int, int], zone_name: str
    ) -> List[Tuple[int, int]]:
        """在指定范围内搜索禁飞区的轨道点"""
        orbit_points = self.map.obstacle_manager.get_orbit_path(zone_name)
        if not orbit_points:
            return []

        nearby_points = []
        pos_2d = (pos[0], pos[1])  # 只考虑2D坐标

        for i, orbit_point in enumerate(orbit_points):
            # 计算距离
            distance = math.sqrt(
                (orbit_point[0] - pos_2d[0]) ** 2 + (orbit_point[1] - pos_2d[1]) ** 2
            )
            if distance <= self.orbit_search_radius:
                nearby_points.append((i, orbit_point))

        return nearby_points

    def _determine_target_quadrants(
        self, cruise_goal: Tuple[int, int, int], zone_name: str
    ) -> List[str]:
        """确定巡航终点所在的象限及相邻象限"""
        orbit_points = self.map.obstacle_manager.get_orbit_path(zone_name)
        quadrants = self.map.obstacle_manager.get_orbit_quadrants(zone_name)

        if not orbit_points or not quadrants:
            return []

        goal_2d = (cruise_goal[0], cruise_goal[1])

        # 找到距离目标最近的轨道点
        min_distance = float("inf")
        closest_quadrant = None

        for quad_name, indices in quadrants.items():
            for idx in indices:
                if idx < len(orbit_points):
                    orbit_point = orbit_points[idx]
                    distance = math.sqrt(
                        (orbit_point[0] - goal_2d[0]) ** 2
                        + (orbit_point[1] - goal_2d[1]) ** 2
                    )
                    if distance < min_distance:
                        min_distance = distance
                        closest_quadrant = quad_name

        if not closest_quadrant:
            return []

        # 定义象限的相邻关系
        quadrant_neighbors = {
            "N": ["NW", "NE"],
            "NE": ["N", "E"],
            "E": ["NE", "SE"],
            "SE": ["E", "S"],
            "S": ["SE", "SW"],
            "SW": ["S", "W"],
            "W": ["SW", "NW"],
            "NW": ["W", "N"],
        }

        # 返回目标象限及其相邻象限
        target_quadrants = [closest_quadrant]
        if closest_quadrant in quadrant_neighbors:
            target_quadrants.extend(quadrant_neighbors[closest_quadrant])

        return target_quadrants

    def _choose_orbit_direction(
        self,
        entry_point_idx: int,
        target_quadrants: List[str],
        zone_name: str,
        cruise_goal: Tuple[int, int, int],
    ) -> int:
        """选择轨道移动方向（顺时针或逆时针）"""
        orbit_points = self.map.obstacle_manager.get_orbit_path(zone_name)
        quadrants = self.map.obstacle_manager.get_orbit_quadrants(zone_name)

        if not orbit_points or not quadrants:
            return 1  # 默认顺时针

        # 找到目标象限中的点
        target_indices = set()
        for quad_name in target_quadrants:
            if quad_name in quadrants:
                target_indices.update(quadrants[quad_name])

        if not target_indices:
            return 1

        # 计算顺时针和逆时针到达目标象限的距离
        clockwise_distance = float("inf")
        counterclockwise_distance = float("inf")

        for target_idx in target_indices:
            # 顺时针距离
            if target_idx >= entry_point_idx:
                cw_dist = target_idx - entry_point_idx
            else:
                cw_dist = len(orbit_points) - entry_point_idx + target_idx

            # 逆时针距离
            if target_idx <= entry_point_idx:
                ccw_dist = entry_point_idx - target_idx
            else:
                ccw_dist = entry_point_idx + len(orbit_points) - target_idx

            clockwise_distance = min(clockwise_distance, cw_dist)
            counterclockwise_distance = min(counterclockwise_distance, ccw_dist)

        # 选择距离更短的方向
        return 1 if clockwise_distance <= counterclockwise_distance else -1

    def _can_exit_orbit_to_goal(
        self,
        current_pos: Tuple[float, float, float],
        cruise_goal: Tuple[float, float, float],
        current_time: int,
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
        occupancy_map,
        step_size: float = 1.0,
    ) -> bool:
        """检查是否可以从当前轨道位置直接朝目标移动"""
        # 使用改进的直线移动方法检查下一步
        next_pos, next_time, obstacle_zone = self._direct_move_towards_goal(
            current_pos,
            cruise_goal,
            current_time,
            min_height,
            agent_id,
            constraints,
            occupancy_map,
            step_size,
        )

        # 如果能成功移动且没有遇到障碍物，则可以退出轨道
        return next_pos is not None and obstacle_zone is None

    def _move_along_orbit(
        self,
        current_orbit_idx: int,
        direction: int,
        zone_name: str,
        current_pos: Tuple[int, int, int],
        current_time: int,
    ) -> Tuple[int, Tuple[int, int, int], int]:
        """沿轨道移动一步"""
        orbit_points = self.map.obstacle_manager.get_orbit_path(zone_name)
        if not orbit_points:
            return current_orbit_idx, current_pos, current_time

        # 计算下一个轨道点索引
        next_orbit_idx = (current_orbit_idx + direction) % len(orbit_points)
        next_orbit_point = orbit_points[next_orbit_idx]

        # 保持当前高度
        next_pos = (next_orbit_point[0], next_orbit_point[1], current_pos[2])
        next_time = current_time + self.cruise_speed_t

        return next_orbit_idx, next_pos, next_time

    def _is_in_target_quadrants(
        self, current_orbit_idx: int, target_quadrants: List[str], zone_name: str
    ) -> bool:
        """检查当前轨道点是否在目标象限中"""
        quadrants = self.map.obstacle_manager.get_orbit_quadrants(zone_name)
        if not quadrants:
            return False

        for quad_name in target_quadrants:
            if quad_name in quadrants and current_orbit_idx in quadrants[quad_name]:
                return True

        return False

    def _direct_move_towards_goal(
        self,
        current_pos: Tuple[float, float, float],
        cruise_goal: Tuple[float, float, float],
        current_time: int,
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
        occupancy_map,
        step_size: float = 1.0,
    ) -> Tuple[Optional[Tuple[float, float, float]], int, Optional[str]]:
        """直线朝目标移动一步，支持浮点数坐标实现精确直线移动

        Args:
            current_pos: 当前位置 (y, x, z)，支持浮点数
            cruise_goal: 目标位置 (y, x, z)，支持浮点数
            current_time: 当前时间
            min_height: 最小高度
            agent_id: 智能体ID
            constraints: 约束条件
            occupancy_map: 占用图
            step_size: 移动步长，默认1.0

        Returns:
            (下一个位置, 下一个时间, 障碍物区域名称)
            如果移动成功，返回(next_pos, next_time, None)
            如果遇到障碍物，返回(None, next_time, obstacle_zone)
        """
        # 计算朝向目标的方向向量
        dy = cruise_goal[0] - current_pos[0]
        dx = cruise_goal[1] - current_pos[1]

        # 计算距离
        distance = math.sqrt(dy**2 + dx**2)

        # 如果已经很接近目标，直接移动到目标
        if distance <= step_size:
            next_pos = (cruise_goal[0], cruise_goal[1], current_pos[2])
        else:
            # 归一化方向向量并按步长移动
            dy_normalized = dy / distance
            dx_normalized = dx / distance

            next_pos = (
                current_pos[0] + dy_normalized * step_size,
                current_pos[1] + dx_normalized * step_size,
                current_pos[2],
            )

        next_time = current_time + self.cruise_speed_t

        # 将浮点坐标转换为整数坐标进行有效性检查
        next_pos_int = (
            int(round(next_pos[0])),
            int(round(next_pos[1])),
            int(round(next_pos[2])),
        )

        # 检查下一个位置是否有效
        is_valid, error = self._is_valid_position(
            next_pos_int, next_time, min_height, agent_id, constraints, occupancy_map
        )

        if is_valid:
            return next_pos, next_time, None
        else:
            # 检查是否遇到了禁飞区
            obstacle_zone = self._get_obstacle_at_position(next_pos_int)
            return None, next_time, obstacle_zone

    def _vertical_takeoff(
        self,
        start: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        start_time: Optional[int],
        occupancy_map,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """执行垂直起飞阶段的路径规划，与jps_v4保持一致"""
        path = []
        current_t = start_time if start_time is not None else 0

        step_size = 5  # 每次跳跃的高度单位
        heights = list(range(start[2], min_height + 1, step_size))
        # 确保包含最后一个元素（最小巡航高度）
        if min_height not in heights:
            heights.append(min_height)

        # 从起点垂直上升到最小巡航高度
        for z in heights:
            pos = (start[0], start[1], z)

            # 检查约束和碰撞，在起飞阶段忽略最小高度限制
            is_valid, error = self._is_valid_position(
                pos,
                current_t,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
                ignore_min_height=True,
            )
            if not is_valid:
                real_z = self.map.grid_converter.relative_to_geo(0, 0, z)["alt"]
                return None, f"垂直爬升阶段在高度 {real_z} {error}"

            # 创建新的路径节点
            node = GridNode3D(start[0], start[1], z, current_t)
            path.append(node)
            current_t += self.takeoff_speed_t

        return path, None

    def _vertical_landing(
        self,
        last_cruise_node: GridNode3D,
        goal: Tuple[int, int, int],
        agent_id: str,
        occupancy_map,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """执行垂直降落阶段的路径规划，与jps_v4保持一致"""
        path = []
        current_t = last_cruise_node.t + self.landing_speed_t

        step_size = -5  # 每次跳跃的高度单位
        heights = list(range(last_cruise_node.z - 1, goal[2] - 1, step_size))
        # 确保包含最后一个元素
        if goal[2] not in heights:
            heights.append(goal[2])

        # 从巡航高度垂直下降到目标点
        for z in heights:
            pos = (goal[0], goal[1], z)

            # 检查约束和碰撞，降落阶段不需要考虑最小高度限制
            is_valid, error = self._is_valid_position(
                pos,
                current_t,
                0,  # 降落阶段不需要考虑最小高度限制
                agent_id,
                constraints,
                occupancy_map,
            )
            if not is_valid:
                real_z = self.map.grid_converter.relative_to_geo(0, 0, z)
                return None, f"降落阶段在高度 {real_z} {error}"

            # 创建新的路径节点
            node = GridNode3D(goal[0], goal[1], z, current_t)
            path.append(node)
            current_t += self.landing_speed_t

        return path, None

    def _find_cruise_path(
        self,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        start_time: Optional[int],
        occupancy_map,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """使用轨道策略进行巡航路径规划"""
        logger.info(f"开始轨道巡航路径规划，从 {start} 到 {goal}")

        # 重置访问过的禁飞区记录
        self.visited_zones.clear()

        path = []
        # 将起始位置转换为浮点数以支持精确移动
        current_pos = (float(start[0]), float(start[1]), float(start[2]))
        current_time = start_time if start_time is not None else 0

        # 当前轨道状态
        current_orbit_zone = None
        current_orbit_idx = -1
        orbit_direction = 1  # 1为顺时针，-1为逆时针
        target_quadrants = []
        orbit_switch_count = 0

        max_iterations = 1000  # 防止无限循环
        iteration = 0

        while iteration < max_iterations:
            iteration += 1

            # 检查是否到达目标（使用小的容差值）
            distance_to_goal = math.sqrt(
                (current_pos[0] - goal[0]) ** 2 + (current_pos[1] - goal[1]) ** 2
            )
            if distance_to_goal < 0.5:  # 容差为0.5个网格单位
                logger.info(f"成功到达巡航目标，总迭代次数: {iteration}")
                break

            # 创建当前位置的节点（转换为整数坐标）
            current_node = GridNode3D(
                int(round(current_pos[0])),
                int(round(current_pos[1])),
                int(round(current_pos[2])),
                current_time,
            )
            path.append(current_node)

            if current_orbit_zone is None:
                # 当前不在轨道上，尝试直线移动
                # 将目标转换为浮点数
                goal_float = (float(goal[0]), float(goal[1]), float(goal[2]))
                next_pos, next_time, obstacle_zone = self._direct_move_towards_goal(
                    current_pos,
                    goal_float,
                    current_time,
                    min_height,
                    agent_id,
                    constraints,
                    occupancy_map,
                )

                if next_pos is not None:
                    # 直线移动成功
                    current_pos = next_pos
                    current_time = next_time
                else:
                    # 遇到障碍物，需要进入轨道
                    if obstacle_zone is None:
                        return None, "巡航阶段遇到未知障碍物"

                    # 检查是否已经访问过这个禁飞区太多次
                    if obstacle_zone in self.visited_zones:
                        orbit_switch_count += 1
                        if orbit_switch_count > self.max_orbit_switches:
                            return None, f"轨道切换次数过多，可能存在死循环"

                    self.visited_zones.add(obstacle_zone)

                    logger.info(f"遇到禁飞区 {obstacle_zone}，开始搜索轨道点")

                    # 搜索附近的轨道点
                    nearby_points = self._find_nearby_orbit_points(
                        current_pos, obstacle_zone
                    )
                    if not nearby_points:
                        return None, f"无法找到禁飞区 {obstacle_zone} 的轨道点"

                    # 选择最近的轨道点作为入口
                    min_distance = float("inf")
                    best_entry = None
                    for idx, orbit_point in nearby_points:
                        distance = math.sqrt(
                            (orbit_point[0] - current_pos[0]) ** 2
                            + (orbit_point[1] - current_pos[1]) ** 2
                        )
                        if distance < min_distance:
                            min_distance = distance
                            best_entry = (idx, orbit_point)

                    if best_entry is None:
                        return None, f"无法确定禁飞区 {obstacle_zone} 的入口轨道点"

                    # 进入轨道
                    current_orbit_zone = obstacle_zone
                    current_orbit_idx = best_entry[0]
                    current_pos = (best_entry[1][0], best_entry[1][1], current_pos[2])
                    current_time += self.cruise_speed_t

                    # 确定目标象限和移动方向
                    target_quadrants = self._determine_target_quadrants(
                        goal, obstacle_zone
                    )
                    orbit_direction = self._choose_orbit_direction(
                        current_orbit_idx, target_quadrants, obstacle_zone, goal
                    )

                    logger.info(
                        f"进入轨道 {obstacle_zone}，索引 {current_orbit_idx}，"
                        f"目标象限 {target_quadrants}，方向 {'顺时针' if orbit_direction == 1 else '逆时针'}"
                    )

            else:
                # 当前在轨道上
                # 检查是否在目标象限且可以退出轨道
                if self._is_in_target_quadrants(
                    current_orbit_idx, target_quadrants, current_orbit_zone
                ) and self._can_exit_orbit_to_goal(
                    current_pos,
                    goal_float,
                    current_time,
                    min_height,
                    agent_id,
                    constraints,
                    occupancy_map,
                ):

                    logger.info(
                        f"在目标象限 {target_quadrants} 中，退出轨道 {current_orbit_zone}"
                    )
                    # 退出轨道
                    current_orbit_zone = None
                    current_orbit_idx = -1
                    target_quadrants = []
                    continue

                # 沿轨道移动
                current_orbit_idx, current_pos, current_time = self._move_along_orbit(
                    current_orbit_idx,
                    orbit_direction,
                    current_orbit_zone,
                    current_pos,
                    current_time,
                )

                # 检查是否进入了新的禁飞区
                new_obstacle_zone = self._get_obstacle_at_position(current_pos)
                if new_obstacle_zone and new_obstacle_zone != current_orbit_zone:
                    logger.info(
                        f"从轨道 {current_orbit_zone} 进入新的禁飞区 {new_obstacle_zone}"
                    )

                    # 检查轨道切换次数
                    if new_obstacle_zone in self.visited_zones:
                        orbit_switch_count += 1
                        if orbit_switch_count > self.max_orbit_switches:
                            return None, f"轨道切换次数过多，可能存在死循环"

                    self.visited_zones.add(new_obstacle_zone)

                    # 切换到新的轨道
                    nearby_points = self._find_nearby_orbit_points(
                        current_pos, new_obstacle_zone
                    )
                    if nearby_points:
                        # 找到最近的轨道点
                        min_distance = float("inf")
                        best_entry = None
                        for idx, orbit_point in nearby_points:
                            distance = math.sqrt(
                                (orbit_point[0] - current_pos[0]) ** 2
                                + (orbit_point[1] - current_pos[1]) ** 2
                            )
                            if distance < min_distance:
                                min_distance = distance
                                best_entry = (idx, orbit_point)

                        if best_entry:
                            current_orbit_zone = new_obstacle_zone
                            current_orbit_idx = best_entry[0]
                            current_pos = (
                                best_entry[1][0],
                                best_entry[1][1],
                                current_pos[2],
                            )

                            # 重新确定目标象限和移动方向
                            target_quadrants = self._determine_target_quadrants(
                                goal, new_obstacle_zone
                            )
                            orbit_direction = self._choose_orbit_direction(
                                current_orbit_idx,
                                target_quadrants,
                                new_obstacle_zone,
                                goal,
                            )

        if iteration >= max_iterations:
            return None, f"巡航路径规划超过最大迭代次数 {max_iterations}"

        # 添加最终节点
        if path and (path[-1].y, path[-1].x) != (goal[0], goal[1]):
            final_node = GridNode3D(goal[0], goal[1], goal[2], current_time)
            path.append(final_node)

        logger.info(f"巡航路径规划完成，路径长度: {len(path)}")
        return path, None

    def find_path(
        self,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int = 0,
        agent_id: str = "0",
        start_time: Optional[int] = None,
        occupancy_map=None,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """主路径规划方法，将路径规划分为三个阶段

        完整的路径规划包括三个阶段：
        1. 垂直起飞：从起点上升到最小巡航高度
        2. 轨道巡航：使用轨道避障策略在固定高度层执行路径搜索
        3. 垂直降落：从巡航高度下降到目标点

        Args:
            start: 起点坐标 (y, x, z)
            goal: 终点坐标 (y, x, z)
            min_height: 最小巡航高度，默认为0
            agent_id: 智能体标识，默认为"0"
            start_time: 起始时间（如果指定），默认为None
            occupancy_map: 用于多机避碰的占用图
            constraints: 时空约束列表，用于多机协调

        Returns:
            Tuple[Optional[List[GridNode3D]], Optional[str]]:
            - 如果成功，返回(完整路径节点列表, None)
            - 如果失败，返回(None, 错误信息)
        """
        logger.info(f"开始轨道路径规划，从 {start} 到 {goal}，最小高度 {min_height}")

        # 1. 检查是否需要垂直起飞
        if start[2] >= min_height:
            # 起始高度已经满足最小高度要求，直接从当前高度开始巡航
            start_node = GridNode3D(
                start[0],
                start[1],
                start[2],
                start_time if start_time is not None else 0,
            )
            takeoff_path = [start_node]
        else:
            # 需要垂直起飞
            takeoff_path, error = self._vertical_takeoff(
                start, min_height, agent_id, start_time, occupancy_map, constraints
            )
            if not takeoff_path:
                return None, f"起飞阶段失败: {error}"

        logger.info(f"起飞阶段完成，路径长度: {len(takeoff_path)}")

        # 2. 巡航阶段
        cruise_goal = (goal[0], goal[1], min_height)  # 在巡航高度的投影点
        cruise_path, error = self._find_cruise_path(
            (takeoff_path[-1].y, takeoff_path[-1].x, takeoff_path[-1].z),
            cruise_goal,
            min_height,
            agent_id,
            takeoff_path[-1].t,  # 使用起飞结束时刻作为巡航开始时间
            occupancy_map,
            constraints,
        )
        if not cruise_path:
            return None, f"巡航阶段失败: {error}"

        logger.info(f"巡航阶段完成，路径长度: {len(cruise_path)}")

        # 3. 垂直降落阶段
        landing_path, error = self._vertical_landing(
            cruise_path[-1], goal, agent_id, occupancy_map, constraints
        )
        if error is not None:
            return None, f"降落阶段失败: {error}"

        logger.info(
            f"降落阶段完成，路径长度: {len(landing_path) if landing_path else 0}"
        )

        # 合并三个阶段的路径，形成完整路径
        complete_path = (
            takeoff_path[:-1] + cruise_path + (landing_path if landing_path else [])
        )

        # 设置父子关系
        for i in range(1, len(complete_path)):
            complete_path[i].parent = complete_path[i - 1]

        logger.info(f"轨道路径规划完成，总路径长度: {len(complete_path)}")
        return complete_path, None
