# cpp_implementation/src/test.py
import math
import sys
import os

# 确保当前目录在 Python 搜索路径中，以便找到 .pyd 文件
# (通常如果 .py 和 .pyd 在同一目录，这是自动的，但显式添加更保险)
# current_dir = os.path.dirname(os.path.abspath(__file__))
# if current_dir not in sys.path:
#     sys.path.insert(0, current_dir)

try:
    # 模块名应与 PYBIND11_MODULE(occupancy_map_cpp, m) 中的 "occupancy_map_cpp" 一致
    import occupancy_map_cpp as omap_cpp

    print("Successfully imported C++ module: occupancy_map_cpp")
except ImportError as e:
    print(f"Error importing C++ module: {e}")
    print(
        f"Please ensure 'occupancy_map_cpp.pyd' is in the same directory as this script ({os.getcwd()}) or in Python's sys.path."
    )
    print(f"Python sys.path: {sys.path}")
    exit()


# 定义 Python GridNode3D 类 (需要与您项目中的 src/core/node_3d.py 定义匹配)
# 我将从之前的对话中复制一个版本。请确保它与您的实际定义一致。
class GridNode3D:
    def __init__(
        self, y=-1, x=-1, z=-1, t=0, g=math.inf, h=math.inf, f=None, parent=None
    ):
        self.y = float(y)
        self.x = float(x)
        self.z = float(z)
        self.t = float(t)  # OccupancyMap C++ 侧主要关心 y, x, z, t
        self.g = g
        self.h = h
        self.f = f if f is not None else float("inf")
        self.parent = parent
        # 如果您的 GridNode3D 还有其他属性，请添加

    def __repr__(self):
        return f"GridNode3D(y={self.y}, x={self.x}, z={self.z}, t={self.t})"


def run_cpp_module_tests():
    print("\n--- Starting Python tests for C++ OccupancyMap module ---")

    # 1. Initialization Test
    print("\n1. C++ OccupancyMap Initialization Test")
    map_size_py = [100, 100, 10]  # Python list [height, width, depth]
    time_buffer_py = 1
    safety_offsets_py = 1

    try:
        cpp_map = omap_cpp.OccupancyMap(map_size_py, time_buffer_py, safety_offsets_py)
        print(f"C++ Map initialized.")
        print(
            f"  Static Height: {omap_cpp.OccupancyMap.get_map_height()}, Expected: {map_size_py[0]}"
        )
        assert omap_cpp.OccupancyMap.get_map_height() == map_size_py[0]
        print(
            f"  Static Width: {omap_cpp.OccupancyMap.get_map_width()}, Expected: {map_size_py[1]}"
        )
        assert omap_cpp.OccupancyMap.get_map_width() == map_size_py[1]
        print(
            f"  Static Depth: {omap_cpp.OccupancyMap.get_map_depth()}, Expected: {map_size_py[2]}"
        )
        assert omap_cpp.OccupancyMap.get_map_depth() == map_size_py[2]
        print(
            f"  Static Time Buffer: {omap_cpp.OccupancyMap.get_time_buffer()}, Expected: {time_buffer_py}"
        )
        assert omap_cpp.OccupancyMap.get_time_buffer() == time_buffer_py
        print("Initialization test PASSED.")
    except Exception as e:
        print(f"Error during C++ OccupancyMap initialization: {e}")
        print("Initialization test FAILED.")
        return

    # 2. Add Path Test
    print("\n2. C++ Add Path Test")
    path1_py_nodes = [
        GridNode3D(y=10, x=10, z=1, t=0),
        GridNode3D(y=10, x=11, z=1, t=1),
        GridNode3D(y=10, x=12, z=1, t=2),
    ]
    agent1_id_py = "agent_py_1"

    try:
        cpp_map.add_path(path1_py_nodes, agent1_id_py)
        print(f"Path added for '{agent1_id_py}' using Python GridNode3D objects.")
        print("Add path test (Python GridNode3D) PASSED.")
    except Exception as e:
        print(f"Error during add_path with Python GridNode3D objects: {e}")
        print("Add path test (Python GridNode3D) FAILED.")
        return

    # You can also test with C++ GridNodeData objects created from Python
    try:
        path1_cpp_data_nodes = [
            omap_cpp.GridNodeData(y=20.0, x=20.0, z=2.0, t=0.0),
            omap_cpp.GridNodeData(y=20.0, x=21.0, z=2.0, t=1.0),
        ]
        agent1_alt_id_py = "agent_py_1_alt"
        cpp_map.add_path(path1_cpp_data_nodes, agent1_alt_id_py)
        print(
            f"Path added for '{agent1_alt_id_py}' using C++ GridNodeData objects created in Python."
        )
        print("Add path test (C++ GridNodeData) PASSED.")
    except Exception as e:
        print(f"Error during add_path with C++ GridNodeData objects: {e}")
        print("Add path test (C++ GridNodeData) FAILED.")
        return

    # 3. Check Collision Test
    print("\n3. C++ Check Collision Test")
    pos_occupied_py = (10, 10, 1)  # Python tuple for position
    time_at_pos_py = 0  # Time for collision check

    try:
        collision_exists, colliding_agent_id = cpp_map.check_collision(
            pos_occupied_py, time_at_pos_py
        )
        print(
            f"Collision at {pos_occupied_py} t={time_at_pos_py}: Exists={collision_exists}, Agent='{colliding_agent_id}'"
        )
        assert collision_exists is True, "Collision expected at (10,10,1) t=0"
        assert (
            colliding_agent_id == agent1_id_py
        ), f"Expected agent '{agent1_id_py}', got '{colliding_agent_id}'"

        pos_free_py = (50, 50, 5)
        time_free_py = 10
        collision_exists2, colliding_agent_id2 = cpp_map.check_collision(
            pos_free_py, time_free_py
        )
        print(
            f"Collision at {pos_free_py} t={time_free_py}: Exists={collision_exists2}, Agent='{colliding_agent_id2}'"
        )
        assert collision_exists2 is False, "No collision expected at (50,50,5) t=10"
        assert (
            colliding_agent_id2 is None
        ), f"Expected no agent, got '{colliding_agent_id2}'"
        print("Check collision test PASSED.")
    except Exception as e:
        print(f"Error during check_collision: {e}")
        print("Check collision test FAILED.")
        return

    # 4. Get Occupying Agents Test
    # ... (add more tests for get_occupying_agents, get_conflict, find_valid_time, remove_agent, clear, copy)
    # For brevity, I'll skip fully re-implementing all tests from the C++ standalone main,
    # but you should expand this based on the logic in cpp_implementation/src/tests/test_occupancy_map.cpp

    print("\n--- Basic Python tests for C++ OccupancyMap module Finished ---")
    print("Consider adding more comprehensive tests for all functionalities.")


if __name__ == "__main__":
    run_cpp_module_tests()
