#pragma once

#include <memory>     // For std::shared_ptr (if used in future, not strictly needed for GridNode* parent)
#include <functional> // For std::hash
#include <cmath>      // For std::fabs in potential future float comparisons

// Forward declaration if GridNode itself is used in a way that creates circular dependency with shared_ptr
// class GridNode; // Not strictly needed here if parent is GridNode*

class GridNode
{
public:
    // Coordinate order changed to y, x, z to match Python and jps_v4.py
    float y, x, z; // coordinate

    float t; // time

    float g, h, f; // Cost

    GridNode *parent; // Pointer to the parent node

    int jump_step;
    bool need_sight;

    // Default constructor
    GridNode()
        : y(0.0f), x(0.0f), z(0.0f), t(0.0f),
          g(0.0f), h(0.0f), f(0.0f),
          parent(nullptr), jump_step(1), need_sight(true) {}

    // Constructor with initial coordinate and time values
    GridNode(float y_val, float x_val, float z_val, float t_val = 0.0f)
        : y(y_val), x(x_val), z(z_val), t(t_val),
          g(0.0f), h(0.0f), f(0.0f),
          parent(nullptr), jump_step(1), need_sight(true) {}

    // Full constructor for JPS relevant values
    GridNode(float y_val, float x_val, float z_val, float t_val,
             float g_val, float h_val, GridNode *p_val = nullptr,
             int j_step = 1, bool n_sight = true)
        : y(y_val), x(x_val), z(z_val), t(t_val),
          g(g_val), h(h_val), f(g_val + h_val), // f is typically g + h
          parent(p_val), jump_step(j_step), need_sight(n_sight)
    {
    }

    // For std::priority_queue (min-heap based on f value, then h for tie-breaking)
    // This defines operator> because std::priority_queue is a max-heap by default.
    // We want lower f values to have higher priority.
    // Note: This operator is for comparing GridNode objects directly.
    // If using pointers in priority_queue, a custom comparator struct is needed (see below).
    bool operator>(const GridNode &other) const
    {
        if (std::fabs(f - other.f) > 1e-6)
        { // Compare floats with tolerance
            return f > other.f;
        }
        return h > other.h; // Tie-breaking: prefer smaller h
    }

    // Hasher for GridNode objects (e.g., for std::unordered_set<GridNode, GridNode::Hasher, GridNode::EqualTo>)
    struct Hasher
    {
        std::size_t operator()(const GridNode &node) const
        {
            // Simple hash combining coordinates
            // Ensure consistent hashing for +0.0 and -0.0 if that's a concern,
            // or if coordinates can be NaN/inf. For typical grid coordinates, this should be fine.
            auto h1 = std::hash<float>{}(node.y);
            auto h2 = std::hash<float>{}(node.x);
            auto h3 = std::hash<float>{}(node.z);
            // A common way to combine hashes
            return h1 ^ (h2 << 1) ^ (h3 << 2);
        }
    };

    // Equality for GridNode objects (e.g., for std::unordered_set<GridNode, GridNode::Hasher, GridNode::EqualTo>)
    // Defines equality based on coordinates for closed set purposes.
    struct EqualTo
    {
        bool operator()(const GridNode &lhs, const GridNode &rhs) const
        {
            // Compare floats with a small tolerance for equality
            // This is important if coordinates might result from calculations.
            // For integer grid indices converted to float, direct comparison might be okay.
            constexpr float epsilon = 1e-6f;
            return (std::fabs(lhs.y - rhs.y) < epsilon &&
                    std::fabs(lhs.x - rhs.x) < epsilon &&
                    std::fabs(lhs.z - rhs.z) < epsilon);
        }
    };
};

// Custom comparator for std::priority_queue of GridNode pointers (GridNode*)
struct CompareGridNodePtrs
{
    bool operator()(const GridNode *lhs, const GridNode *rhs) const
    {
        // Handle null pointers if they can exist in the queue
        if (!lhs)
            return false; // nulls effectively have lowest priority (go to bottom of max-heap)
        if (!rhs)
            return true; // non-nulls have higher priority than nulls

        // Primary sort by f value (ascending for min-heap behavior)
        if (std::fabs(lhs->f - rhs->f) > 1e-6)
        {
            return lhs->f > rhs->f;
        }
        // Secondary sort by h value (ascending for tie-breaking)
        return lhs->h > rhs->h;
    }
};

// Hasher for GridNode pointers (e.g. for std::unordered_set<GridNode*, GridNodePtrHasher, GridNodePtrEqualTo>)
// Hashes based on the content (coordinates) of the pointed-to GridNode.
struct GridNodePtrHasher
{
    std::size_t operator()(const GridNode *node) const
    {
        if (!node)
            return 0; // Or some other value for nullptr
        auto h1 = std::hash<float>{}(node->y);
        auto h2 = std::hash<float>{}(node->x);
        auto h3 = std::hash<float>{}(node->z);
        return h1 ^ (h2 << 1) ^ (h3 << 2);
    }
};

// Equality for GridNode pointers (e.g. for std::unordered_set<GridNode*, GridNodePtrHasher, GridNodePtrEqualTo>)
// Compares based on the content (coordinates) of the pointed-to GridNodes.
struct GridNodePtrEqualTo
{
    bool operator()(const GridNode *lhs, const GridNode *rhs) const
    {
        if (lhs == rhs)
            return true; // Same pointer or both null
        if (!lhs || !rhs)
            return false; // One is null, the other isn't

        constexpr float epsilon = 1e-6f;
        return (std::fabs(lhs->y - rhs->y) < epsilon &&
                std::fabs(lhs->x - rhs->x) < epsilon &&
                std::fabs(lhs->z - rhs->z) < epsilon);
    }
};
