#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/stl_optional.h>
#include "grid_node.h"
#include "occupancy_map.h"
#include "grid_node_data.h"
#include "time_interval.h"

namespace py = pybind11;

PYBIND11_MODULE(occupancy_map_cpp, m)
{
    m.doc() = "C++ Occupancy Map and related data structures";

    py::class_<GridNode>(m, "GridNode")
        .def(py::init<float, float, float, float, float, float, GridNode *, int, bool>(),
             py::arg("y"), py::arg("x"), py::arg("z"),
             py::arg("t") = 0.0f, py::arg("g") = 0.0f, py::arg("h") = 0.0f,
             py::arg("parent") = nullptr, py::arg("jump_step") = 5, py::arg("need_sight") = false,
             py::keep_alive<1, 8>())
        .def_readwrite("y", &GridNode::y)
        .def_readwrite("x", &GridNode::x)
        .def_readwrite("z", &GridNode::z)
        .def_readwrite("t", &GridNode::t)
        .def_readwrite("g", &GridNode::g)
        .def_readwrite("h", &GridNode::h)
        .def_readwrite("f", &GridNode::f)
        .def_readwrite("parent", &GridNode::parent)
        .def_readwrite("jump_step", &GridNode::jump_step)
        .def_readwrite("need_sight", &GridNode::need_sight)
        .def("__eq__", [](const GridNode &self, const GridNode &other)
             { return self.y == other.y && self.x == other.x && self.z == other.z && self.t == other.t; })
        .def("__hash__", [](const GridNode &self)
             { return py::hash(py::make_tuple(self.y, self.x, self.z, self.t)); })
        .def_property_readonly("coords", [](const GridNode &self)
                               { return py::make_tuple(self.y, self.x, self.z); });

    py::enum_<ConstraintType>(m, "ConstraintType")
        .value("Vertex", ConstraintType::Vertex)
        .value("Edge", ConstraintType::Edge)
        .export_values();

    py::class_<VertexConstraintData>(m, "VertexConstraintData")
        .def(py::init<std::string, float, float, float, float>(),
             py::arg("agent_id"), py::arg("y"), py::arg("x"), py::arg("z"), py::arg("t"))
        .def_readwrite("agent_id", &VertexConstraintData::agent_id)
        .def_readwrite("y", &VertexConstraintData::y)
        .def_readwrite("x", &VertexConstraintData::x)
        .def_readwrite("z", &VertexConstraintData::z)
        .def_readwrite("t", &VertexConstraintData::t);

    py::class_<EdgeConstraintData>(m, "EdgeConstraintData")
        .def(py::init<std::string, float, float, float, float, float, float, float, float>(),
             py::arg("agent_id"), py::arg("y1"), py::arg("x1"), py::arg("z1"), py::arg("t1"),
             py::arg("y2"), py::arg("x2"), py::arg("z2"), py::arg("t2"))
        .def_readwrite("agent_id", &EdgeConstraintData::agent_id)
        .def_readwrite("y1", &EdgeConstraintData::y1)
        .def_readwrite("x1", &EdgeConstraintData::x1)
        .def_readwrite("z1", &EdgeConstraintData::z1)
        .def_readwrite("t1", &EdgeConstraintData::t1)
        .def_readwrite("y2", &EdgeConstraintData::y2)
        .def_readwrite("x2", &EdgeConstraintData::x2)
        .def_readwrite("z2", &EdgeConstraintData::z2)
        .def_readwrite("t2", &EdgeConstraintData::t2);

    py::class_<Constraint>(m, "Constraint")
        .def(py::init<ConstraintType, VertexConstraintData, EdgeConstraintData>(),
             py::arg("type"), py::arg("v_data"), py::arg("e_data"))
        .def_readwrite("type", &Constraint::type)
        .def_readwrite("v_data", &Constraint::v_data)
        .def_readwrite("e_data", &Constraint::e_data)
        .def_static("vertex", [](const std::string &agent_id, float y, float x, float z, float t)
                    { return Constraint{ConstraintType::Vertex, VertexConstraintData{agent_id, y, x, z, t}, EdgeConstraintData{}}; })
        .def_static("edge", [](const std::string &agent_id, float y1, float x1, float z1, float t1, float y2, float x2, float z2, float t2)
                    { return Constraint{ConstraintType::Edge, VertexConstraintData{}, EdgeConstraintData{agent_id, y1, x1, z1, t1, y2, x2, z2, t2}}; });

    py::class_<TimeInterval>(m, "TimeInterval")
        .def(py::init<float, float>(), py::arg("start_time"), py::arg("end_time"))
        .def_readwrite("start_time", &TimeInterval::start_time)
        .def_readwrite("end_time", &TimeInterval::end_time)
        .def("__repr__",
             [](const TimeInterval &ti)
             {
                 return "<TimeInterval start=" + std::to_string(ti.start_time) +
                        " end=" + std::to_string(ti.end_time) + ">";
             });

    py::class_<OccupancyMap, std::shared_ptr<OccupancyMap>>(m, "OccupancyMap")
        .def(py::init<int, int, int, float, float, float, float>(),
             py::arg("height"), py::arg("width"), py::arg("depth"),
             py::arg("resolution") = 1.0f, py::arg("origin_y") = 0.0f,
             py::arg("origin_x") = 0.0f, py::arg("origin_z") = 0.0f)
        .def_property_readonly("height", &OccupancyMap::get_height)
        .def_property_readonly("width", &OccupancyMap::get_width)
        .def_property_readonly("depth", &OccupancyMap::get_depth)
        .def("set_obstacle", &OccupancyMap::set_obstacle, py::arg("y"), py::arg("x"), py::arg("z"))
        .def("remove_obstacle", &OccupancyMap::remove_obstacle, py::arg("y"), py::arg("x"), py::arg("z"))
        .def("is_traversable", &OccupancyMap::is_traversable, py::arg("y"), py::arg("x"), py::arg("z"))
        .def("add_path_segment", &OccupancyMap::add_path_segment,
             py::arg("agent_id"), py::arg("p1"), py::arg("p2"))
        .def("remove_path_segment", &OccupancyMap::remove_path_segment,
             py::arg("agent_id"), py::arg("p1"), py::arg("p2"))
        .def("check_collision", [](const OccupancyMap &self, float y, float x, float z, float t)
             {
            std::string colliding_agent_id;
            bool collision = self.check_collision(y, x, z, t, colliding_agent_id);
            if (collision) {
                return py::make_tuple(true, colliding_agent_id);
            }
            return py::make_tuple(false, py::none()); }, py::arg("y"), py::arg("x"), py::arg("z"), py::arg("t"), "Checks for collision at a point in spacetime")
        .def("is_path_segment_colliding", &OccupancyMap::is_path_segment_colliding, py::arg("p1"), py::arg("p2"), py::arg("agent_id_to_ignore") = "")
        .def("get_map_data_tuple", &OccupancyMap::get_map_data_tuple)
        .def("load_map_data_tuple", &OccupancyMap::load_map_data_tuple, py::arg("map_data_tuple"));
}
