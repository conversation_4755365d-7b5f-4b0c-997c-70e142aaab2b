#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轨道路径规划算法测试脚本
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.pathfinding.orbit import OrbitPathFinder
from src.core.map.map_handler_3d import Map3D
from src.config.settings import initialize_settings
import numpy as np


def create_test_map():
    """创建测试地图"""
    # 初始化配置
    try:
        initialize_settings()
    except Exception as e:
        print(f"配置初始化失败，使用默认参数: {e}")

    # 创建3D地图，使用默认参数
    map3d = Map3D(height=1000, width=1000, depth=100, fixed_obstacles=False)

    # 添加一些测试禁飞区
    # 圆形禁飞区
    map3d.add_solid_cylindrical_no_fly_zone(
        center_lat=39.5, center_lon=116.5, radius_meters=200, zone_name="circular_nfz_1"
    )

    # 多边形禁飞区
    polygon_coords = [
        {"lat": 39.3, "lng": 116.3},
        {"lat": 39.3, "lng": 116.7},
        {"lat": 39.7, "lng": 116.7},
        {"lat": 39.7, "lng": 116.3},
    ]
    map3d.add_solid_polygon_no_fly_zone(polygon_coords, zone_name="polygon_nfz_1")

    return map3d


def test_orbit_pathfinding():
    """测试轨道路径规划"""
    print("开始测试轨道路径规划算法...")

    # 创建测试地图
    map3d = create_test_map()

    # 创建轨道路径规划器
    orbit_finder = OrbitPathFinder(map3d)

    # 定义起点和终点（网格坐标）
    start = (100, 100, 5)  # 起点
    goal = (800, 800, 5)  # 终点
    min_height = 50  # 最小巡航高度

    print(f"起点: {start}")
    print(f"终点: {goal}")
    print(f"最小巡航高度: {min_height}")

    # 执行路径规划
    try:
        path, error = orbit_finder.find_path(
            start=start,
            goal=goal,
            min_height=min_height,
            agent_id="test_drone",
            start_time=0,
        )

        if path:
            print(f"路径规划成功！")
            print(f"路径长度: {len(path)} 个节点")
            print(f"起始节点: ({path[0].y}, {path[0].x}, {path[0].z}) at t={path[0].t}")
            print(
                f"结束节点: ({path[-1].y}, {path[-1].x}, {path[-1].z}) at t={path[-1].t}"
            )

            # 分析路径阶段
            takeoff_nodes = [node for node in path if node.z < min_height]
            cruise_nodes = [
                node
                for node in path
                if node.z >= min_height and (node.y, node.x) != (goal[0], goal[1])
            ]
            landing_nodes = [
                node
                for node in path
                if node.z >= min_height and (node.y, node.x) == (goal[0], goal[1])
            ]

            print(f"起飞阶段节点数: {len(takeoff_nodes)}")
            print(f"巡航阶段节点数: {len(cruise_nodes)}")
            print(f"降落阶段节点数: {len(landing_nodes)}")

            # 检查路径连续性
            for i in range(1, len(path)):
                prev_node = path[i - 1]
                curr_node = path[i]
                distance = (
                    (curr_node.y - prev_node.y) ** 2
                    + (curr_node.x - prev_node.x) ** 2
                    + (curr_node.z - prev_node.z) ** 2
                ) ** 0.5
                if distance > 10:  # 如果距离过大，可能有问题
                    print(f"警告: 节点 {i-1} 到 {i} 距离过大: {distance:.2f}")

            return True

        else:
            print(f"路径规划失败: {error}")
            return False

    except Exception as e:
        print(f"路径规划过程中发生错误: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_orbit_methods():
    """测试轨道相关方法"""
    print("\n开始测试轨道相关方法...")

    # 创建测试地图
    map3d = create_test_map()
    orbit_finder = OrbitPathFinder(map3d)

    # 测试获取禁飞区信息
    test_pos = (500, 500, 50)
    obstacle_zone = orbit_finder._get_obstacle_at_position(test_pos)
    print(f"位置 {test_pos} 的障碍物类型: {obstacle_zone}")

    # 如果有禁飞区，测试轨道点搜索
    if obstacle_zone:
        nearby_points = orbit_finder._find_nearby_orbit_points(test_pos, obstacle_zone)
        print(f"禁飞区 {obstacle_zone} 附近的轨道点数量: {len(nearby_points)}")

        if nearby_points:
            print(f"最近的几个轨道点:")
            for i, (idx, point) in enumerate(nearby_points[:5]):
                print(f"  索引 {idx}: {point}")

        # 测试象限确定
        goal = (800, 800, 50)
        target_quadrants = orbit_finder._determine_target_quadrants(goal, obstacle_zone)
        print(f"目标 {goal} 对应的象限: {target_quadrants}")

        if nearby_points and target_quadrants:
            # 测试方向选择
            entry_idx = nearby_points[0][0]
            direction = orbit_finder._choose_orbit_direction(
                entry_idx, target_quadrants, obstacle_zone, goal
            )
            print(f"选择的轨道方向: {'顺时针' if direction == 1 else '逆时针'}")


def main():
    """主函数"""
    print("=" * 50)
    print("轨道路径规划算法测试")
    print("=" * 50)

    # 测试轨道相关方法
    test_orbit_methods()

    # 测试完整路径规划
    success = test_orbit_pathfinding()

    print("\n" + "=" * 50)
    if success:
        print("测试完成：轨道路径规划算法工作正常！")
    else:
        print("测试失败：轨道路径规划算法存在问题。")
    print("=" * 50)


if __name__ == "__main__":
    main()
