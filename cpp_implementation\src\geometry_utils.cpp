#include "geometry_utils.h" // Should be "../include/geometry_utils.h"
#include <limits>           // For std::numeric_limits
#include <algorithm>        // For std::min, std::max

namespace CVToolbox
{
    namespace GeometryUtils
    {

        // --- Line Segment Operations (Helper for Ray Casting) ---
        int orientation(const Point2D &p, const Point2D &q, const Point2D &r)
        {
            double val = (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);
            if (std::abs(val) < std::numeric_limits<double>::epsilon())
                return 0;             // Collinear
            return (val > 0) ? 1 : 2; // Clockwise or Counterclockwise
        }

        bool on_segment(const Point2D &p, const Point2D &q, const Point2D &r)
        {
            return (q.x <= std::max(p.x, r.x) && q.x >= std::min(p.x, r.x) &&
                    q.y <= std::max(p.y, r.y) && q.y >= std::min(p.y, r.y));
        }

        bool segments_intersect(const Point2D &p1, const Point2D &q1, const Point2D &p2, const Point2D &q2)
        {
            int o1 = orientation(p1, q1, p2);
            int o2 = orientation(p1, q1, q2);
            int o3 = orientation(p2, q2, p1);
            int o4 = orientation(p2, q2, q1);

            // General case
            if (o1 != 0 && o2 != 0 && o3 != 0 && o4 != 0)
            {
                return (o1 != o2 && o3 != o4);
            }

            // Special Cases for collinear points
            if (o1 == 0 && on_segment(p1, p2, q1))
                return true;
            if (o2 == 0 && on_segment(p1, q2, q1))
                return true;
            if (o3 == 0 && on_segment(p2, p1, q2))
                return true;
            if (o4 == 0 && on_segment(p2, q1, q2))
                return true;

            return false; // Doesn't fall in any of the above cases
        }

        // --- Polygon Operations ---
        bool is_point_in_polygon_precise(const Point2D &point, const Polygon2D &polygon)
        {
            if (polygon.size() < 3)
            {
                return false; // A polygon must have at least 3 vertices
            }

            int n = polygon.size();
            if (n == 0)
                return false;

            // Create a point for ray segment from point to infinite
            Point2D extreme = {point.y, std::numeric_limits<double>::max()}; // Ray goes to the right

            int intersections = 0;
            for (int i = 0; i < n; ++i)
            {
                Point2D p1 = polygon[i];
                Point2D p2 = polygon[(i + 1) % n];

                // Check if the ray intersects with the edge (p1, p2)
                if (segments_intersect(point, extreme, p1, p2))
                {
                    // If the point is collinear with an edge, check if it's on the segment
                    if (orientation(p1, point, p2) == 0)
                    {
                        return on_segment(p1, point, p2);
                    }
                    intersections++;
                }
            }
            // If intersections is odd, point is inside. If even, point is outside.
            return (intersections % 2 == 1);
        }

        std::vector<bool> points_in_polygon_vectorized(const std::vector<Point2D> &points, const Polygon2D &polygon)
        {
            std::vector<bool> results;
            results.reserve(points.size());
            for (const auto &p : points)
            {
                results.push_back(is_point_in_polygon_precise(p, polygon));
            }
            return results;
        }

        Polygon2D expand_polygon(const Polygon2D &polygon, double expand_distance)
        {
            if (polygon.size() < 3 || expand_distance == 0.0)
            {
                return polygon; // Nothing to expand or not a polygon
            }

            Polygon2D expanded_polygon;
            expanded_polygon.reserve(polygon.size());
            int n = polygon.size();

            // Calculate area to determine winding order (for normal direction)
            // This is a simplified version of the Python logic's area calculation
            double area = 0.0;
            for (int i = 0; i < n; ++i)
            {
                const Point2D &p1 = polygon[i];
                const Point2D &p2 = polygon[(i + 1) % n];
                area += (p1.x * p2.y - p2.x * p1.y);
            }
            area /= 2.0;

            // normal_sign: -1 for counter-clockwise (area > 0), 1 for clockwise (area < 0)
            // This ensures normals point outwards.
            // Python's _expand_polygon uses: normal_sign = -1 if area > 0 else 1
            // If area > 0 (CCW), Python's normal is (dy, -dx).
            // If area < 0 (CW), Python's normal is (-dy, dx).
            // Let's assume edge vector is (dx, dy) = p_next - p_curr.
            // CCW normal: (-dy, dx). CW normal: (dy, -dx).
            // Python's normal_sign seems to be applied to (-dy, dx) for CCW.
            // So if area > 0 (CCW), normal_sign = -1, gives (dy, -dx).
            // If area < 0 (CW), normal_sign = 1, gives (-dy, dx).
            // This is consistent.
            double normal_sign = (area > 0) ? -1.0 : 1.0;
            if (std::abs(area) < std::numeric_limits<double>::epsilon())
            { // Collinear points, can't reliably expand
                // Fallback: could try to compute convex hull first, or return original
                return polygon;
            }

            for (int i = 0; i < n; ++i)
            {
                const Point2D &prev_p = polygon[(i + n - 1) % n];
                const Point2D &curr_p = polygon[i];
                const Point2D &next_p = polygon[(i + 1) % n];

                Point2D vec1 = {curr_p.y - prev_p.y, curr_p.x - prev_p.x}; // prev_p -> curr_p
                Point2D vec2 = {next_p.y - curr_p.y, next_p.x - curr_p.x}; // curr_p -> next_p

                double len1_sq = vec1.length_sq();
                double len2_sq = vec2.length_sq();

                if (len1_sq < std::numeric_limits<double>::epsilon() || len2_sq < std::numeric_limits<double>::epsilon())
                {
                    // Degenerate edge, just add current point or handle error
                    // For simplicity, let's try to push based on a simple outward vector if possible
                    // This part needs more robust handling similar to Python's fallback
                    Point2D center = {0, 0};
                    for (const auto &pt : polygon)
                    {
                        center.y += pt.y;
                        center.x += pt.x;
                    }
                    center.y /= n;
                    center.x /= n;
                    Point2D outward_vec = {curr_p.y - center.y, curr_p.x - center.x};
                    double outward_len_sq = outward_vec.length_sq();
                    if (outward_len_sq > std::numeric_limits<double>::epsilon())
                    {
                        double outward_len = std::sqrt(outward_len_sq);
                        expanded_polygon.push_back({curr_p.y + outward_vec.y / outward_len * expand_distance,
                                                    curr_p.x + outward_vec.x / outward_len * expand_distance});
                    }
                    else
                    {
                        expanded_polygon.push_back(curr_p); // Cannot determine direction
                    }
                    continue;
                }

                double len1 = std::sqrt(len1_sq);
                double len2 = std::sqrt(len2_sq);

                // Normals to the edges (pointing outwards based on Python's logic)
                // Edge1 (prev_p -> curr_p): normal_sign * (-vec1.x/len1, vec1.y/len1)
                // Edge2 (curr_p -> next_p): normal_sign * (-vec2.x/len2, vec2.y/len2)
                // Python: normal_sign * -prev_edge_normalized[1], normal_sign * prev_edge_normalized[0]
                // If prev_edge_normalized = (vec1.x/len1, vec1.y/len1)
                // Then normal1_py = (normal_sign * -vec1.y/len1, normal_sign * vec1.x/len1)
                Point2D norm1 = {normal_sign * -vec1.x / len1, normal_sign * vec1.y / len1}; // Normal to edge (prev_p, curr_p)
                Point2D norm2 = {normal_sign * -vec2.x / len2, normal_sign * vec2.y / len2}; // Normal to edge (curr_p, next_p)

                // Bisector of the angle between the two normals
                Point2D bisector = {norm1.y + norm2.y, norm1.x + norm2.x}; // Sum of normals
                double bisector_len_sq = bisector.length_sq();

                if (bisector_len_sq < std::numeric_limits<double>::epsilon())
                {
                    // Normals are opposite (collinear segment), push along one normal
                    expanded_polygon.push_back({curr_p.y + norm1.y * expand_distance, curr_p.x + norm1.x * expand_distance});
                }
                else
                {
                    double bisector_len = std::sqrt(bisector_len_sq);
                    Point2D bisector_normalized = {bisector.y / bisector_len, bisector.x / bisector_len};

                    // cos_angle = dot(bisector_normalized, norm1)
                    // Python: cos_angle = np.dot(bisector_normalized, prev_normal)
                    // Here, prev_normal corresponds to norm1.
                    double cos_half_angle = bisector_normalized.dot(norm1); // Cos of angle between bisector and one normal
                                                                            // This is cos(alpha/2) where alpha is angle between normals
                                                                            // We need 1/cos(theta/2) where theta is the internal angle of polygon vertex
                                                                            // Or, 1/sin(phi/2) where phi is the exterior angle.
                                                                            // The Python code uses cos_angle = dot(bisector, normal_edge1).
                                                                            // This is effectively sin of the angle between edge1 and bisector.
                                                                            // Or cos of angle between normal1 and bisector.

                    double adjusted_distance = expand_distance;
                    if (std::abs(cos_half_angle) > 1e-9)
                    {   // Avoid division by zero
                        // The Python code's adjusted_distance = expand_distance / abs(cos_angle)
                        // This factor is 1/cos(angle_between_bisector_and_normal).
                        // For a convex corner, this angle is small, so cos is near 1.
                        // For a reflex corner, this angle is larger.
                        // This seems to be a miter offset calculation.
                        adjusted_distance = expand_distance / std::abs(cos_half_angle);
                    }
                    // Python code has: adjusted_distance = min(adjusted_distance, expand_distance * 3)
                    adjusted_distance = std::min(adjusted_distance, expand_distance * 3.0);

                    expanded_polygon.push_back({curr_p.y + bisector_normalized.y * adjusted_distance,
                                                curr_p.x + bisector_normalized.x * adjusted_distance});
                }
            }
            return expanded_polygon;
        }

    } // namespace GeometryUtils
} // namespace CVToolbox
