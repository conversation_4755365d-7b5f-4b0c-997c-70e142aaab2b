#pragma once

#include <list>
#include <unordered_map>
#include <stdexcept> // For std::range_error

template <typename Key, typename Value>
class LRUCache
{
public:
    LRUCache(size_t capacity) : capacity_(capacity)
    {
        if (capacity_ == 0)
        {
            throw std::range_error("LRUCache capacity must be positive.");
        }
    }

    // Gets an item from the cache. Returns true if found, false otherwise.
    // If found, value is copied to the output parameter `value_out`.
    bool get(const Key &key, Value &value_out)
    {
        auto it = item_map_.find(key);
        if (it == item_map_.end())
        {
            return false; // Key not in cache
        }

        // Key found, move it to the front of the list (most recently used)
        // The list stores (key, value) pairs. We splice the found item's node
        // to the beginning of the list.
        item_list_.splice(item_list_.begin(), item_list_, it->second);

        // Update the value_out parameter
        value_out = it->second->second;
        return true;
    }

    // Puts an item into the cache.
    void put(const Key &key, const Value &value)
    {
        auto it = item_map_.find(key);

        if (it != item_map_.end())
        {
            // Key already exists, update its value and move to front
            it->second->second = value;
            item_list_.splice(item_list_.begin(), item_list_, it->second);
            return;
        }

        // Key does not exist
        // Check if cache is full
        if (item_list_.size() >= capacity_)
        {
            // Cache is full, evict the least recently used item (at the back of the list)
            Key lru_key = item_list_.back().first;
            item_list_.pop_back();
            item_map_.erase(lru_key);
        }

        // Add the new item to the front of the list and to the map
        item_list_.emplace_front(key, value);
        item_map_[key] = item_list_.begin();
    }

    // Returns the current size of the cache
    size_t size() const
    {
        return item_list_.size();
    }

    // Clears the cache
    void clear()
    {
        item_list_.clear();
        item_map_.clear();
    }

private:
    // Doubly linked list to store (key, value) pairs.
    // The front of the list is the most recently used item.
    // The back of the list is the least recently used item.
    std::list<std::pair<Key, Value>> item_list_;

    // Hash map to store key to list iterator mapping for O(1) access.
    // The iterator points to the corresponding (key, value) pair in item_list_.
    std::unordered_map<Key, typename std::list<std::pair<Key, Value>>::iterator> item_map_;

    size_t capacity_;
};
