#include "time_interval.h"
#include <algorithm> // For std::min and std::max

TimeInterval::TimeInterval(int start_time, int end_time, const std::string &id)
    : start(start_time), end(end_time), agent_id(id) {}

bool TimeInterval::overlaps(const TimeInterval &other) const
{
    // Check if one interval is completely to the left of the other's start
    // or if one interval is completely to the right of the other's end.
    // If neither is true, they overlap.
    return !(this->end < other.start || other.end < this->start);
}

std::optional<TimeInterval> TimeInterval::merge(const TimeInterval &other) const
{
    if (this->agent_id != other.agent_id)
    {
        return std::nullopt; // Cannot merge intervals from different agents
    }

    // Python version merges if self.end + 1 >= other.start (continuous or overlapping)
    // This means if other.start is at most self.end + 1
    if (other.start > this->end + 1 && this->start > other.end + 1)
    {                        // Check both directions for non-contiguous
        return std::nullopt; // Intervals are not overlapping or continuous
    }

    // Ensure they are indeed overlapping or continuous in at least one direction
    // This check simplifies to: if they don't overlap AND they are not continuous
    bool are_overlapping_or_continuous = (this->start <= other.end + 1) && (other.start <= this->end + 1);

    if (!are_overlapping_or_continuous)
    {
        return std::nullopt;
    }

    return TimeInterval(std::min(this->start, other.start),
                        std::max(this->end, other.end),
                        this->agent_id);
}
