from setuptools import setup, Extension
from setuptools.command.build_ext import build_ext
import pybind11
import os

# Define the C++ extension module for OccupancyMap and related types
occupancy_map_module = Extension(
    "occupancy_map_cpp",  # Name of the module
    sources=[
        "cpp_implementation/src/bindings_occupancy_map.cpp",
        "cpp_implementation/src/occupancy_map.cpp",
        "cpp_implementation/src/time_interval.cpp",
        # grid_node.cpp if it exists and is not header-only
    ],
    include_dirs=[
        pybind11.get_include(),
        "cpp_implementation/include",
    ],
    language="c++",
    extra_compile_args=["-std=c++17", "-O3"],
    extra_link_args=[],
)

# Define the C++ extension module for OptimizedPathFinder (JPS v4)
jps_v4_module = Extension(
    "jps_v4_cpp",  # Name of the module
    sources=[
        "cpp_implementation/src/bindings_jps_v4.cpp",
        "cpp_implementation/src/optimized_path_finder.cpp",
    ],
    include_dirs=[
        pybind11.get_include(),
        "cpp_implementation/include",
    ],
    language="c++",
    extra_compile_args=["-std=c++17", "-O3"],
    extra_link_args=[],
)


# Custom build_ext command to place the .so/.pyd file in the src directory
class BuildExt(build_ext):
    def get_ext_fullpath(self, ext_name):
        # This method is called for each extension.
        # ext_name will be "occupancy_map_cpp" or "jps_v4_cpp"

        # We want the compiled files (e.g., occupancy_map_cpp.pyd, jps_v4_cpp.pyd)
        # to be placed in the 'src' directory so they can be imported by
        # Python modules within 'src' like `from occupancy_map_cpp import ...`

        filename = os.path.basename(super().get_ext_fullpath(ext_name))
        # Place it in 'src' directory relative to setup.py
        return os.path.join(os.path.dirname(os.path.abspath(__file__)), "src", filename)


setup(
    name="MultiAgentPathfinding",
    version="0.1.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="Multi-Agent Pathfinding System with C++ backend",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    ext_modules=[occupancy_map_module, jps_v4_module],  # List both modules
    cmdclass={
        "build_ext": BuildExt,
    },
    packages=[
        "src",
        "src.core",
        "src.core.map",
        "src.core.pathfinding",
        "src.handlers",
        "src.handlers.message_handlers",
        "src.utils",
        "src.tests",
    ],
    package_dir={"": "."},
    python_requires=">=3.8",
    zip_safe=False,
)
