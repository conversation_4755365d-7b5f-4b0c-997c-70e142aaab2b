0000000000000000000000000000000000000000 8885ee5fbc52f637450e3002e2746f2275f4a546 liuchang1230 <<EMAIL>> 1737446275 +0800	branch: Created from HEAD
8885ee5fbc52f637450e3002e2746f2275f4a546 75ef07188a3c3560a9138b24b1dc6207978bd136 liuchang1230 <<EMAIL>> 1737446303 +0800	commit: Add time safety margin and update occupancy map logic: - Add time safety margin for better path planning - Update occupancy map logic for improved collision avoidance - Modify related components to support these changes
75ef07188a3c3560a9138b24b1dc6207978bd136 3b178d7d660f3394897a136be7392a07b5497385 liuchang1230 <<EMAIL>> 1737537141 +0800	commit: Add route validator and update core components: - Add route_validator.py for path validation - Update path planning logic in multiple files - Improve occupancy map handling
3b178d7d660f3394897a136be7392a07b5497385 e7837081617dead61af871c25651fc3eaad6d31a liuchang1230 <<EMAIL>> 1737684081 +0800	commit: Update path planning components: - Improve node handling and occupancy map logic - Remove deprecated files (mydebug.py, occupancy_map_3d.py) - Enhance route validation and testing - Update Kafka consumer for better request handling
e7837081617dead61af871c25651fc3eaad6d31a d129566186a99b5899fd25fdda37ff821e3ca7cd liuchang1230 <<EMAIL>> 1737857005 +0800	commit: Update optimized_path_finder.py: optimize path finding algorithm
