#ifndef POLYGON_INDEX_H
#define POLYGON_INDEX_H

#include <string>
#include <vector>
#include <map>
#include "geometry_types.h" // For Point2D, Polygon2D, BoundingBox2D

namespace CVToolbox
{

    class PolygonIndexCpp
    {
    public:
        PolygonIndexCpp() = default; // Default constructor is fine

        /**
         * @brief Adds a polygon with a given ID. Computes and stores its bounding box.
         * If a polygon with the same ID already exists, it will be replaced.
         * @param polygon_id Unique identifier for the polygon.
         * @param vertices Vector of Point2D representing the polygon's vertices.
         */
        void add_polygon(const std::string &polygon_id, const Polygon2D &vertices);

        /**
         * @brief Removes a polygon and its bounding box by ID.
         * @param polygon_id The ID of the polygon to remove.
         * @return True if the polygon was found and removed, false otherwise.
         */
        bool remove_polygon(const std::string &polygon_id);

        /**
         * @brief Retrieves the vertices of a polygon by its ID.
         * @param polygon_id The ID of the polygon.
         * @param out_vertices Output parameter to store the vertices if found.
         * @return True if the polygon was found, false otherwise.
         */
        bool get_polygon_vertices(const std::string &polygon_id, Polygon2D &out_vertices) const;

        /**
         * @brief Retrieves the bounding box of a polygon by its ID.
         * @param polygon_id The ID of the polygon.
         * @param out_bounds Output parameter to store the bounding box if found.
         * @return True if the polygon was found, false otherwise.
         */
        bool get_polygon_bounds(const std::string &polygon_id, BoundingBox2D &out_bounds) const;

        /**
         * @brief Gets a list of all polygon IDs currently stored.
         * @return Vector of strings containing all polygon IDs.
         */
        std::vector<std::string> get_all_polygon_ids() const;

        /**
         * @brief Finds all polygons whose bounding boxes contain the given point.
         * This is a fast pre-filtering step before precise point-in-polygon checks.
         * @param point The Point2D to check.
         * @return Vector of polygon IDs whose bounding boxes contain the point.
         */
        std::vector<std::string> get_candidate_polygons(const Point2D &point) const;

        /**
         * @brief Removes all polygons and their bounding boxes from the index.
         */
        void clear();

        /**
         * @brief Checks if a polygon with the given ID exists.
         * @param polygon_id The ID to check.
         * @return True if the polygon exists, false otherwise.
         */
        bool has_polygon(const std::string &polygon_id) const;

        // Public members for direct access if absolutely necessary, though using getters is preferred.
        // These mirror the Python version's accessibility for `polygons` and `bounds`.
    public:
        std::map<std::string, Polygon2D> polygons_;
        std::map<std::string, BoundingBox2D> bounds_;
    };

} // namespace CVToolbox
#endif // POLYGON_INDEX_H
