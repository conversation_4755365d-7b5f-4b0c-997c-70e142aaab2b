#include "optimized_path_finder.h"
#include <cmath>     // For std::fabs, std::sqrt, std::round, std::ceil
#include <algorithm> // For std::min, std::max, std::reverse
#include <limits>    // For std::numeric_limits
#include <tuple>     // For std::tuple (cache key)

// Define static const members if any (like DIRECTION_PRIORITIES)
const std::vector<OptimizedPathFinder::Direction> OptimizedPathFinder::DIRECTION_PRIORITIES = {
    // XY plane basic directions
    {0.0f, 1.0f, 0.0f},  // Right
    {0.0f, -1.0f, 0.0f}, // Left
    {1.0f, 0.0f, 0.0f},  // Forward (assuming +y is forward based on (y,x,z))
    {-1.0f, 0.0f, 0.0f}, // Backward
    // XY plane diagonal directions
    {1.0f, 1.0f, 0.0f},   // Forward-Right
    {1.0f, -1.0f, 0.0f},  // Forward-Left
    {-1.0f, 1.0f, 0.0f},  // Backward-Right
    {-1.0f, -1.0f, 0.0f}, // Backward-Left
    // Z-axis movements
    {0.0f, 0.0f, 1.0f}, // Up
    {0.0f, 0.0f, -1.0f} // Down
};

OptimizedPathFinder::OptimizedPathFinder(
    const OccupancyMap &map_ref,
    double takeoff_speed,
    double cruise_speed,
    double landing_speed,
    bool only_turning_points,
    int max_jps_steps,
    bool need_smooth,
    double smoothness_factor,
    int cache_size) : map_(map_ref),
                      takeoff_speed_t_(0), cruise_speed_t_(0), landing_speed_t_(0), // Will be initialized below
                      only_turning_points_(only_turning_points),
                      max_jps_steps_(max_jps_steps), // Max steps/distance for a single jump
                      need_smooth_(need_smooth),
                      smoothness_factor_(smoothness_factor),
                      constraint_cache_(cache_size > 0 ? static_cast<size_t>(cache_size) : 10000),
                      static_neighbor_cache_(50000) // Default size from Python, or make configurable
{
    if (takeoff_speed <= 0 || cruise_speed <= 0 || landing_speed <= 0)
    {
        throw std::invalid_argument("Speeds must be positive.");
    }
    takeoff_speed_t_ = 1.0 / takeoff_speed;
    cruise_speed_t_ = 1.0 / cruise_speed;
    landing_speed_t_ = 1.0 / landing_speed;

    node_pool_.reserve(2048);
    next_node_idx_ = 0;
}

GridNode *OptimizedPathFinder::allocate_node(float y, float x, float z, float t, float g, float h, GridNode *parent, int jump_step, bool need_sight)
{
    if (next_node_idx_ >= node_pool_.size())
    {
        node_pool_.emplace_back();
    }

    GridNode &node = node_pool_[next_node_idx_++];
    node.y = y;
    node.x = x;
    node.z = z;
    node.t = t;
    node.g = g;
    node.h = h;
    node.f = g + h;
    node.parent = parent;
    node.jump_step = jump_step;
    node.need_sight = need_sight;
    return &node;
}

void OptimizedPathFinder::reset_node_pool()
{
    next_node_idx_ = 0;
}

float OptimizedPathFinder::cost_between_coords(float y1, float x1, float z1, float y2, float x2, float z2) const
{
    float dy = std::fabs(y2 - y1);
    float dx = std::fabs(x2 - x1);
    float dz = std::fabs(z2 - z1);

    if (dz > 1e-6)
    {
        return dz;
    }
    else
    {
        float diag_xy = std::min(dx, dy);
        float straight_xy = std::max(dx, dy) - diag_xy;
        return diag_xy * 1.41421356f + straight_xy;
    }
}

std::vector<std::vector<GridNode *>> OptimizedPathFinder::get_neighbors(
    GridNode *current_node_ptr,
    int min_height,
    const std::string &agent_id,
    const std::vector<Constraint> &constraints,
    int vertical_look_steps)
{
    using PathSegmentData = std::vector<std::tuple<float, float, float>>;
    std::vector<PathSegmentData> static_segments_data;

    auto cache_key = std::make_tuple(current_node_ptr->y, current_node_ptr->x, current_node_ptr->z, current_node_ptr->jump_step);

    if (!static_neighbor_cache_.get(cache_key, static_segments_data))
    {
        std::vector<Direction> blocked_horizontal_dirs_info;

        for (const auto &dir : DIRECTION_PRIORITIES)
        {
            if (std::fabs(dir.dz) < 1e-6)
            {
                float ny = current_node_ptr->y + dir.dy * current_node_ptr->jump_step;
                float nx = current_node_ptr->x + dir.dx * current_node_ptr->jump_step;
                float nz = current_node_ptr->z;

                if (ny >= map_.height_ || nx >= map_.width_ || nz >= map_.depth_ || ny < 0 || nx < 0 || nz < 0)
                {
                    continue;
                }
                if (current_node_ptr->parent &&
                    std::fabs(ny - current_node_ptr->parent->y) < 1e-6 &&
                    std::fabs(nx - current_node_ptr->parent->x) < 1e-6 &&
                    std::fabs(nz - current_node_ptr->parent->z) < 1e-6)
                {
                    continue;
                }

                if (map_.is_traversable(static_cast<int>(std::round(ny)), static_cast<int>(std::round(nx)), static_cast<int>(std::round(nz))))
                {
                    static_segments_data.push_back({{ny, nx, nz}});
                }
                else
                {
                    blocked_horizontal_dirs_info.push_back(dir);
                }
            }
        }

        for (const auto &blocked_dir : blocked_horizontal_dirs_info)
        {
            for (int step_up_amount = 1; step_up_amount <= vertical_look_steps; step_up_amount += 2)
            {
                PathSegmentData vertical_evasion_segment_data;

                float viy = current_node_ptr->y;
                float vix = current_node_ptr->x;
                float viz = current_node_ptr->z + static_cast<float>(step_up_amount);

                float hty = current_node_ptr->y + blocked_dir.dy * current_node_ptr->jump_step;
                float htx = current_node_ptr->x + blocked_dir.dx * current_node_ptr->jump_step;
                float htz = viz;

                if (hty >= map_.height_ || htx >= map_.width_ || htz >= map_.depth_ || hty < 0 || htx < 0 || htz < 0 ||
                    viz >= map_.depth_ || viz < 0)
                {
                    continue;
                }

                if (map_.is_traversable(static_cast<int>(std::round(viy)), static_cast<int>(std::round(vix)), static_cast<int>(std::round(viz))) &&
                    map_.is_traversable(static_cast<int>(std::round(hty)), static_cast<int>(std::round(htx)), static_cast<int>(std::round(htz))))
                {

                    vertical_evasion_segment_data.push_back({viy, vix, viz});
                    vertical_evasion_segment_data.push_back({hty, htx, htz});
                    static_segments_data.push_back(vertical_evasion_segment_data);
                    break;
                }
            }
        }
        static_neighbor_cache_.put(cache_key, static_segments_data);
    }

    std::vector<std::vector<GridNode *>> valid_neighbor_paths;
    for (const auto &seg_data : static_segments_data)
    {
        bool segment_is_dynamically_valid = true;
        std::vector<GridNode *> current_path_segment_nodes;

        GridNode *prev_node_in_segment_ptr = current_node_ptr;
        float accumulated_time_in_segment = current_node_ptr->t;
        float accumulated_g_in_segment = current_node_ptr->g;

        for (const auto &pos_tuple : seg_data)
        {
            float py = std::get<0>(pos_tuple);
            float px = std::get<1>(pos_tuple);
            float pz = std::get<2>(pos_tuple);

            float cost = cost_between_coords(prev_node_in_segment_ptr->y, prev_node_in_segment_ptr->x, prev_node_in_segment_ptr->z, py, px, pz);
            float time_step = cost * cruise_speed_t_;

            float new_node_t = accumulated_time_in_segment + time_step;
            float new_node_g = accumulated_g_in_segment + cost;

            auto validity_check = is_valid_position(py, px, pz, new_node_t, min_height, agent_id, constraints);
            if (!validity_check.first)
            {
                segment_is_dynamically_valid = false;
                break;
            }

            GridNode *actual_parent = (current_path_segment_nodes.empty()) ? current_node_ptr : current_path_segment_nodes.back();
            GridNode *new_seg_node = allocate_node(py, px, pz, new_node_t, new_node_g, 0.0f, actual_parent, current_node_ptr->jump_step);
            current_path_segment_nodes.push_back(new_seg_node);

            accumulated_time_in_segment = new_node_t;
            accumulated_g_in_segment = new_node_g;
            prev_node_in_segment_ptr = new_seg_node;
        }

        if (segment_is_dynamically_valid && !current_path_segment_nodes.empty())
        {
            valid_neighbor_paths.push_back(current_path_segment_nodes);
        }
    }
    return valid_neighbor_paths;
}

GridNode *OptimizedPathFinder::jump(
    GridNode *current_node_in_path,
    GridNode *parent_for_direction,
    const GridNode &goal_node,
    int min_height,
    const std::string &agent_id,
    const std::vector<Constraint> &constraints,
    int jump_step_val,
    int vertical_look_steps)
{
    if (!current_node_in_path)
        return nullptr;

    if (std::fabs(current_node_in_path->y - goal_node.y) < 1e-6 &&
        std::fabs(current_node_in_path->x - goal_node.x) < 1e-6)
    {
        if (std::fabs(current_node_in_path->z - goal_node.z) < 1e-6)
        {
            return current_node_in_path;
        }
    }

    if (!parent_for_direction)
    {
        return current_node_in_path;
    }

    float offset_y = current_node_in_path->y - parent_for_direction->y;
    float offset_x = current_node_in_path->x - parent_for_direction->x;

    float norm_dy = (std::fabs(offset_y) > 1e-6) ? (offset_y / std::fabs(offset_y)) : 0.0f;
    float norm_dx = (std::fabs(offset_x) > 1e-6) ? (offset_x / std::fabs(offset_x)) : 0.0f;

    if (std::fabs(norm_dy) < 1e-6 && std::fabs(norm_dx) < 1e-6)
    {
        return current_node_in_path;
    }

    float current_dy_step = norm_dy * jump_step_val;
    float current_dx_step = norm_dx * jump_step_val;
    float current_dz_step = 0.0f;

    GridNode *P_current_end_of_jump = current_node_in_path;
    float accumulated_jump_distance = 0.0f;

    while (true)
    {
        float next_y = P_current_end_of_jump->y + current_dy_step;
        float next_x = P_current_end_of_jump->x + current_dx_step;
        float next_z = P_current_end_of_jump->z + current_dz_step;

        float cost_this_step = cost_between_coords(
            P_current_end_of_jump->y, P_current_end_of_jump->x, P_current_end_of_jump->z,
            next_y, next_x, next_z);

        float next_t = P_current_end_of_jump->t + cruise_speed_t_ * cost_this_step;
        float next_g = P_current_end_of_jump->g + cost_this_step;

        GridNode *P_next_candidate = nullptr;
        auto validity = is_valid_position(next_y, next_x, next_z, next_t, min_height, agent_id, constraints);

        if (!validity.first && std::fabs(current_dz_step) < 1e-6)
        {
            if (validity.second && validity.second.value().find("out of map bounds") != std::string::npos)
            {
                P_current_end_of_jump->need_sight = (accumulated_jump_distance == 0.0f) ? false : true;
                return P_current_end_of_jump;
            }
            if (!map_.is_traversable(static_cast<int>(std::round(next_y)), static_cast<int>(std::round(next_x)), static_cast<int>(std::round(next_z))))
            {
                P_current_end_of_jump->need_sight = false;
                return P_current_end_of_jump;
            }

            bool found_vertical_path = false;
            for (int s = 1; s <= vertical_look_steps; s += 2)
            {
                float viy = P_current_end_of_jump->y;
                float vix = P_current_end_of_jump->x;
                float viz = P_current_end_of_jump->z + static_cast<float>(s);
                float v_cost = static_cast<float>(s);
                float vt = P_current_end_of_jump->t + cruise_speed_t_ * v_cost;
                float vg = P_current_end_of_jump->g + v_cost;

                auto v_validity = is_valid_position(viy, vix, viz, vt, min_height, agent_id, constraints);
                if (v_validity.first)
                {
                    GridNode *P_up_node = allocate_node(viy, vix, viz, vt, vg, 0.0f, P_current_end_of_jump, jump_step_val);

                    float hty = next_y;
                    float htx = next_x;
                    float htz = viz;
                    float h_cost = cost_between_coords(viy, vix, viz, hty, htx, htz);
                    float ht = vt + cruise_speed_t_ * h_cost;
                    float hg = vg + h_cost;

                    auto h_validity = is_valid_position(hty, htx, htz, ht, min_height, agent_id, constraints);
                    if (h_validity.first)
                    {
                        P_next_candidate = allocate_node(hty, htx, htz, ht, hg, 0.0f, P_up_node, jump_step_val);
                        current_dz_step = 0.0f;
                        current_dy_step = norm_dy * jump_step_val;
                        current_dx_step = norm_dx * jump_step_val;
                        found_vertical_path = true;
                        break;
                    }
                }
            }
            if (!found_vertical_path)
            {
                P_current_end_of_jump->need_sight = false;
                return P_current_end_of_jump;
            }
        }
        else if (validity.first)
        {
            P_next_candidate = allocate_node(next_y, next_x, next_z, next_t, next_g, 0.0f, P_current_end_of_jump, jump_step_val);
        }
        else
        {
            P_current_end_of_jump->need_sight = false;
            if (validity.second && validity.second.value().find("out of map bounds") != std::string::npos)
            {
                P_current_end_of_jump->need_sight = (accumulated_jump_distance == 0.0f) ? false : true;
            }
            return P_current_end_of_jump;
        }

        if (!P_next_candidate)
        {
            P_current_end_of_jump->need_sight = false;
            return P_current_end_of_jump;
        }
        P_current_end_of_jump = P_next_candidate;

        if (std::fabs(P_current_end_of_jump->y - goal_node.y) < 1e-6 &&
            std::fabs(P_current_end_of_jump->x - goal_node.x) < 1e-6)
        {
            if (std::fabs(P_current_end_of_jump->z - goal_node.z) < 1e-6)
            {
                return P_current_end_of_jump;
            }
        }

        accumulated_jump_distance += jump_step_val;

        if (accumulated_jump_distance > max_jps_steps_)
        {
            return P_current_end_of_jump;
        }

        float next_potential_y = P_current_end_of_jump->y + current_dy_step;
        float next_potential_x = P_current_end_of_jump->x + current_dx_step;
        float next_potential_z = P_current_end_of_jump->z + current_dz_step;

        GridNode temp_next_potential_node(next_potential_y, next_potential_x, next_potential_z);
        if (octile_distance(P_current_end_of_jump, &goal_node) - octile_distance(&temp_next_potential_node, &goal_node) <= 1e-6)
        {
            return P_current_end_of_jump;
        }
    }
    return P_current_end_of_jump;
}

bool OptimizedPathFinder::has_line_of_sight(const GridNode *p1, const GridNode *p2, int min_height, const std::string &agent_id, const std::vector<Constraint> &constraints)
{
    if (!p1 || !p2)
        return false;

    float y1_f = p1->y;
    float x1_f = p1->x;
    float z1_f = p1->z;
    float t1_f = p1->t;
    float y2_f = p2->y;
    float x2_f = p2->x;
    float z2_f = p2->z;
    float t2_f = p2->t;

    float delta_y = y2_f - y1_f;
    float delta_x = x2_f - x1_f;
    float delta_z = z2_f - z1_f;
    float delta_t = t2_f - t1_f;

    float max_delta_coord = std::max({std::fabs(delta_x), std::fabs(delta_y), std::fabs(delta_z)});
    int num_steps = static_cast<int>(std::ceil(max_delta_coord));

    if (num_steps == 0)
    {
        auto p1_validity = is_valid_position(y1_f, x1_f, z1_f, t1_f, min_height, agent_id, constraints);
        if (!p1_validity.first)
            return false;
        auto p2_validity = is_valid_position(y2_f, x2_f, z2_f, t2_f, min_height, agent_id, constraints);
        return p2_validity.first;
    }

    for (int i = 0; i <= num_steps; ++i)
    {
        float progress = static_cast<float>(i) / num_steps;
        float curr_y_f = y1_f + delta_y * progress;
        float curr_x_f = x1_f + delta_x * progress;
        float curr_z_f = z1_f + delta_z * progress;
        float curr_t = t1_f + delta_t * progress;

        int grid_y = static_cast<int>(std::round(curr_y_f));
        int grid_x = static_cast<int>(std::round(curr_x_f));
        int grid_z = static_cast<int>(std::round(curr_z_f));

        if (!map_.is_traversable(grid_y, grid_x, grid_z))
        {
            return false;
        }

        auto valid_pos = is_valid_position(curr_y_f, curr_x_f, curr_z_f, curr_t, min_height, agent_id, constraints);
        if (!valid_pos.first)
        {
            return false;
        }
    }
    return true;
}

std::vector<GridNode> OptimizedPathFinder::smooth_path(
    const std::vector<GridNode> &path,
    int min_height,
    const std::string &agent_id,
    const std::vector<Constraint> &constraints,
    double smoothness_factor)
{
    if (path.size() < 3)
    {
        return path;
    }

    std::vector<GridNode> smoothed_path;
    smoothed_path.push_back(path[0]);

    size_t current_index = 0;
    while (current_index < path.size() - 1)
    {
        bool can_smooth = false;
        size_t best_smooth_index = current_index + 1;

        // Iterate backwards from the end of the path to find the furthest reachable point
        for (size_t next_index = path.size() - 1; next_index > current_index + 1; --next_index)
        {
            const GridNode &p1 = smoothed_path.back(); // Current anchor point in smoothed_path
            const GridNode &p2 = path[next_index];     // Potential next point

            float direct_cost = octile_distance(&p1, &p2);
            float path_segment_cost = 0.0f;
            for (size_t j = current_index; j < next_index; ++j)
            {
                // Need to use path[j] for segment cost calculation relative to original path
                // If using smoothed_path.back() as p1, then path_segment_cost should be from path[current_index]
                path_segment_cost += octile_distance(&path[j], &path[j + 1]);
            }

            // Adjust p1 for LOS check to be path[current_index] to match segment cost logic
            if (direct_cost < path_segment_cost * smoothness_factor)
            {
                if (has_line_of_sight(&path[current_index], &p2, min_height, agent_id, constraints))
                {
                    best_smooth_index = next_index;
                    can_smooth = true;
                    break;
                }
            }
        }

        if (can_smooth)
        {
            // Update time and g for the smoothed segment
            GridNode new_smoothed_node = path[best_smooth_index]; // Copy
            const GridNode &prev_smoothed_node = smoothed_path.back();
            float cost_to_new_node = octile_distance(&prev_smoothed_node, &new_smoothed_node);

            new_smoothed_node.t = prev_smoothed_node.t + cruise_speed_t_ * cost_to_new_node; // Assuming cruise speed for smoothed segments
            new_smoothed_node.g = prev_smoothed_node.g + cost_to_new_node;
            // Parentage in smoothed path is implicit by order, actual parent pointers from original path are lost here.
            // This is fine as smoothed_path is a sequence of coordinates/times.

            smoothed_path.push_back(new_smoothed_node);
            current_index = best_smooth_index;
        }
        else
        {
            // Cannot smooth, just add the next point from the original path
            // Update its time and g relative to the last point in smoothed_path
            GridNode next_original_node = path[current_index + 1]; // Copy
            const GridNode &prev_smoothed_node = smoothed_path.back();
            float cost_to_next_original = octile_distance(&prev_smoothed_node, &next_original_node);

            next_original_node.t = prev_smoothed_node.t + cruise_speed_t_ * cost_to_next_original;
            next_original_node.g = prev_smoothed_node.g + cost_to_next_original;

            smoothed_path.push_back(next_original_node);
            current_index++;
        }
    }
    return smoothed_path;
}

// --- Main Pathfinding Method ---
std::pair<std::pair<std::vector<GridNode>, std::vector<GridNode>>, std::optional<std::string>>
OptimizedPathFinder::find_path(
    const GridNode &start_node_py,
    const GridNode &goal_node_py,
    int min_height,
    const std::string &agent_id,
    const std::vector<Constraint> &constraints)
{
    reset_node_pool();

    std::vector<GridNode *> takeoff_path_ptrs;
    std::optional<std::string> error_msg;
    GridNode *takeoff_last_node_ptr = nullptr;

    if (start_node_py.z >= min_height)
    {
        takeoff_last_node_ptr = allocate_node(start_node_py.y, start_node_py.x, start_node_py.z, start_node_py.t, 0.0f, 0.0f, nullptr, start_node_py.jump_step);
        if (!takeoff_last_node_ptr)
            return {{}, "Failed to allocate start node for takeoff."};
        takeoff_path_ptrs.push_back(takeoff_last_node_ptr);
    }
    else
    {
        auto takeoff_result = vertical_takeoff(start_node_py, min_height, agent_id, constraints);
        takeoff_path_ptrs = takeoff_result.first;
        error_msg = takeoff_result.second;
        if (error_msg)
        {
            return {{}, "Takeoff phase failed: " + *error_msg};
        }
        if (takeoff_path_ptrs.empty())
        {
            return {{}, "Takeoff phase failed: Path is empty."};
        }
        takeoff_last_node_ptr = takeoff_path_ptrs.back();
    }

    if (!takeoff_last_node_ptr)
    {
        return {{}, "Takeoff phase failed: No last node."};
    }

    GridNode cruise_goal_pos_node(goal_node_py.y, goal_node_py.x, static_cast<float>(min_height), 0.0f);

    auto cruise_result = find_cruise_path(takeoff_last_node_ptr, cruise_goal_pos_node, min_height, agent_id, constraints);
    std::vector<GridNode *> cruise_path_ptrs = cruise_result.first;
    error_msg = cruise_result.second;

    if (error_msg)
    {
        return {{}, "Cruise phase failed: " + *error_msg};
    }
    if (cruise_path_ptrs.empty())
    {
        bool at_cruise_goal = std::fabs(takeoff_last_node_ptr->y - cruise_goal_pos_node.y) < 1e-6 &&
                              std::fabs(takeoff_last_node_ptr->x - cruise_goal_pos_node.x) < 1e-6 &&
                              std::fabs(takeoff_last_node_ptr->z - cruise_goal_pos_node.z) < 1e-6;
        if (!at_cruise_goal)
        {
            return {{}, "Cruise phase failed: Path is empty and not at goal."};
        }
        cruise_path_ptrs.push_back(takeoff_last_node_ptr);
    }
    GridNode *cruise_last_node_ptr = cruise_path_ptrs.back();
    if (!cruise_last_node_ptr)
    {
        return {{}, "Cruise phase failed: No last node."};
    }

    auto landing_result = vertical_landing(cruise_last_node_ptr, goal_node_py, agent_id, constraints);
    std::vector<GridNode *> landing_path_ptrs = landing_result.first;
    error_msg = landing_result.second;
    if (error_msg)
    {
        return {{}, "Landing phase failed: " + *error_msg};
    }

    std::vector<GridNode *> full_path_ptrs;
    if (!takeoff_path_ptrs.empty())
    {
        full_path_ptrs.insert(full_path_ptrs.end(), takeoff_path_ptrs.begin(),
                              takeoff_path_ptrs.end() - (!cruise_path_ptrs.empty() && cruise_path_ptrs.front() == takeoff_path_ptrs.back() ? 1 : 0));
    }
    if (!cruise_path_ptrs.empty())
    {
        full_path_ptrs.insert(full_path_ptrs.end(), cruise_path_ptrs.begin(),
                              cruise_path_ptrs.end() - (!landing_path_ptrs.empty() && landing_path_ptrs.front() == cruise_path_ptrs.back() ? 1 : 0));
    }
    if (!landing_path_ptrs.empty())
    {
        full_path_ptrs.insert(full_path_ptrs.end(), landing_path_ptrs.begin(), landing_path_ptrs.end());
    }

    if (full_path_ptrs.empty() && takeoff_last_node_ptr)
    {
        full_path_ptrs.push_back(takeoff_last_node_ptr);
    }

    if (full_path_ptrs.empty())
    {
        return {{}, "Resulting path is empty after combining stages."};
    }

    std::vector<GridNode> final_complete_path_objs;
    if (full_path_ptrs.back() != nullptr)
    {
        final_complete_path_objs = reconstruct_path_objects(full_path_ptrs.back());
    }
    else if (!full_path_ptrs.empty() && full_path_ptrs.front() != nullptr)
    {                                                                                // Fallback if last is null but path exists
        final_complete_path_objs = reconstruct_path_objects(full_path_ptrs.front()); // Or handle error
    }
    else
    {
        return {{}, "Failed to reconstruct path, final node is null."};
    }

    std::vector<GridNode> final_turning_points_objs;

    if (need_smooth_ && final_complete_path_objs.size() > 2)
    {
        final_complete_path_objs = smooth_path(final_complete_path_objs, min_height, agent_id, constraints, smoothness_factor_);
    }

    if (!final_complete_path_objs.empty())
    {
        auto turning_points_data = extract_turning_points_from_nodes(final_complete_path_objs);
        final_turning_points_objs = turning_points_data.first;
    }

    if (only_turning_points_)
    {
        return {{final_turning_points_objs, final_turning_points_objs}, std::nullopt};
    }

    return {{final_complete_path_objs, final_turning_points_objs}, std::nullopt};
}

std::vector<GridNode> OptimizedPathFinder::reconstruct_path_objects(GridNode *target_node_ptr)
{
    std::vector<GridNode> path;
    GridNode *current = target_node_ptr;
    while (current != nullptr)
    {
        path.push_back(*current);
        current = current->parent;
    }
    std::reverse(path.begin(), path.end());
    return path;
}

std::pair<bool, std::optional<std::string>> OptimizedPathFinder::is_valid_position(
    float y, float x, float z, float t,
    int min_height,
    const std::string &agent_id,
    const std::vector<Constraint> &constraints,
    bool ignore_min_height)
{
    if (y >= map_.height_ || x >= map_.width_ || z >= map_.depth_ ||
        y < 0 || x < 0 || z < 0)
    {
        return {false, "Position out of map bounds"};
    }

    if (!map_.is_traversable(static_cast<int>(std::round(y)), static_cast<int>(std::round(x)), static_cast<int>(std::round(z))))
    {
        return {false, "Position is not traversable (static obstacle)"};
    }

    if (!ignore_min_height && z < min_height)
    {
        return {false, "Below minimum cruise height"};
    }

    std::tuple<float, float, float, float> cache_key = {y, x, z, t};
    std::pair<bool, std::optional<std::string>> cached_result;
    if (constraint_cache_.get(cache_key, cached_result))
    {
        return cached_result;
    }

    GridNode current_node_obj(y, x, z, t);
    auto dynamic_check_result = check_constraints_and_collisions(current_node_obj, agent_id, constraints);
    constraint_cache_.put(cache_key, dynamic_check_result);
    return dynamic_check_result;
}

std::pair<bool, std::optional<std::string>> OptimizedPathFinder::check_constraints_and_collisions(
    const GridNode &node,
    const std::string &agent_id,
    const std::vector<Constraint> &constraints)
{
    std::string colliding_agent_id_out;
    bool has_collision = map_.check_collision(node.y, node.x, node.z, node.t, colliding_agent_id_out);
    if (has_collision && colliding_agent_id_out != agent_id)
    {
        return {false, "Collision with agent " + colliding_agent_id_out + " at time " + std::to_string(node.t)};
    }

    for (const auto &constraint : constraints)
    {
        if (constraint.type == ConstraintType::Vertex)
        {
            const auto &c_data = constraint.v_data;
            if (c_data.agent_id == agent_id &&
                std::fabs(node.y - c_data.y) < 1e-6 &&
                std::fabs(node.x - c_data.x) < 1e-6 &&
                std::fabs(node.z - c_data.z) < 1e-6 &&
                std::fabs(node.t - c_data.t) < 1e-6)
            {
                return {false, "Vertex constraint violation"};
            }
        }
    }
    return {true, std::nullopt};
}

std::pair<std::vector<GridNode *>, std::optional<std::string>> OptimizedPathFinder::vertical_takeoff(
    const GridNode &start_pos_node,
    int min_height,
    const std::string &agent_id,
    const std::vector<Constraint> &constraints)
{
    std::vector<GridNode *> path_ptrs;
    float current_t = start_pos_node.t;
    float start_y = start_pos_node.y;
    float start_x = start_pos_node.x;
    float current_z = start_pos_node.z;
    const float step_size = 5.0f;

    GridNode *prev_node = nullptr;
    if (std::fabs(current_z - start_pos_node.z) < 1e-6)
    {
        auto valid_check_start = is_valid_position(start_y, start_x, current_z, current_t, min_height, agent_id, constraints, true);
        if (!valid_check_start.first)
        {
            return {{}, "Takeoff failed at initial z=" + std::to_string(current_z) + ": " + *valid_check_start.second};
        }
        prev_node = allocate_node(start_y, start_x, current_z, current_t, 0.0f, 0.0f, nullptr, start_pos_node.jump_step);
        if (!prev_node)
            return {{}, "Takeoff failed: initial node allocation error"};
        path_ptrs.push_back(prev_node);
    }

    while (current_z < min_height)
    {
        float actual_step = step_size;
        if (current_z + step_size > min_height)
        {
            actual_step = static_cast<float>(min_height) - current_z;
        }
        current_z += actual_step;
        current_t += takeoff_speed_t_ * actual_step;

        auto valid_check = is_valid_position(start_y, start_x, current_z, current_t, min_height, agent_id, constraints, true);
        if (!valid_check.first)
        {
            return {{}, "Takeoff failed at z=" + std::to_string(current_z) + ": " + *valid_check.second};
        }
        GridNode *new_node = allocate_node(start_y, start_x, current_z, current_t, (prev_node ? prev_node->g : 0.0f) + actual_step, 0.0f, prev_node, start_pos_node.jump_step);
        if (!new_node)
            return {{}, "Takeoff failed: node allocation error"};
        path_ptrs.push_back(new_node);
        prev_node = new_node;

        if (std::fabs(current_z - static_cast<float>(min_height)) < 1e-6)
            break;
    }
    return {path_ptrs, std::nullopt};
}

std::pair<std::vector<GridNode *>, std::optional<std::string>> OptimizedPathFinder::vertical_landing(
    GridNode *last_cruise_node,
    const GridNode &goal_pos_node,
    const std::string &agent_id,
    const std::vector<Constraint> &constraints)
{
    std::vector<GridNode *> path_ptrs;
    if (!last_cruise_node)
        return {{}, "Landing failed: last cruise node is null."};

    float current_t = last_cruise_node->t;
    float goal_y = goal_pos_node.y;
    float goal_x = goal_pos_node.x;
    float goal_z_final = goal_pos_node.z;
    float current_z = last_cruise_node->z;
    const float step_size = 5.0f;

    GridNode *prev_node = last_cruise_node;
    bool cruise_was_single_point_at_goal_height = (last_cruise_node->parent == nullptr || last_cruise_node->parent->z != last_cruise_node->z);

    if (std::fabs(current_z - goal_z_final) < 1e-6)
    {
        if (std::fabs(last_cruise_node->y - goal_y) > 1e-6 || std::fabs(last_cruise_node->x - goal_x) > 1e-6)
        {
            float cost = cost_between_coords(last_cruise_node->y, last_cruise_node->x, current_z, goal_y, goal_x, current_z);
            current_t += landing_speed_t_ * cost;
            auto valid_check = is_valid_position(goal_y, goal_x, goal_z_final, current_t, 0, agent_id, constraints, true);
            if (!valid_check.first)
            {
                return {{}, "Landing failed at final goal z=" + std::to_string(goal_z_final) + ": " + *valid_check.second};
            }
            GridNode *final_node = allocate_node(goal_y, goal_x, goal_z_final, current_t, prev_node->g + cost, 0.0f, prev_node, goal_pos_node.jump_step);
            if (!final_node)
                return {{}, "Landing failed: final node allocation error"};
            path_ptrs.push_back(final_node);
        }
        else if (path_ptrs.empty() && cruise_was_single_point_at_goal_height)
        {
        }
        return {path_ptrs, std::nullopt};
    }

    while (current_z > goal_z_final)
    {
        float actual_step = step_size;
        if (current_z - step_size < goal_z_final)
        {
            actual_step = current_z - goal_z_final;
        }
        current_z -= actual_step;
        current_t += landing_speed_t_ * actual_step;

        auto valid_check = is_valid_position(goal_y, goal_x, current_z, current_t, 0, agent_id, constraints, true);
        if (!valid_check.first)
        {
            return {{}, "Landing failed at z=" + std::to_string(current_z) + ": " + *valid_check.second};
        }
        GridNode *new_node = allocate_node(goal_y, goal_x, current_z, current_t, prev_node->g + actual_step, 0.0f, prev_node, goal_pos_node.jump_step);
        if (!new_node)
            return {{}, "Landing failed: node allocation error"};
        path_ptrs.push_back(new_node);
        prev_node = new_node;

        if (std::fabs(current_z - goal_z_final) < 1e-6)
            break;
    }
    return {path_ptrs, std::nullopt};
}

std::pair<std::vector<GridNode *>, std::optional<std::string>> OptimizedPathFinder::find_cruise_path(
    GridNode *start_jps_node,
    const GridNode &goal_cruise_pos_node,
    int min_height,
    const std::string &agent_id,
    const std::vector<Constraint> &constraints)
{
    if (!start_jps_node)
        return {{}, "Cruise start node is null."};

    if (std::fabs(start_jps_node->y - goal_cruise_pos_node.y) < 1e-6 &&
        std::fabs(start_jps_node->x - goal_cruise_pos_node.x) < 1e-6 &&
        std::fabs(start_jps_node->z - goal_cruise_pos_node.z) < 1e-6)
    {
        return {{start_jps_node}, std::nullopt};
    }

    float estimated_goal_time = start_jps_node->t + cruise_speed_t_ * octile_distance(start_jps_node, &goal_cruise_pos_node);
    auto goal_valid_check = is_valid_position(goal_cruise_pos_node.y, goal_cruise_pos_node.x, goal_cruise_pos_node.z,
                                              estimated_goal_time, min_height, agent_id, constraints);
    if (!goal_valid_check.first)
    {
        return {{}, "Cruise goal is not valid: " + *goal_valid_check.second};
    }

    std::priority_queue<GridNode *, std::vector<GridNode *>, CompareGridNodePtrs> open_list;
    std::unordered_set<GridNode *, GridNodePtrHasher, GridNodePtrEqualTo> closed_set;

    start_jps_node->g = 0;
    start_jps_node->h = octile_distance(start_jps_node, &goal_cruise_pos_node);
    start_jps_node->f = start_jps_node->g + start_jps_node->h;

    if (start_jps_node->h < 25.0f)
    {
        start_jps_node->jump_step = 1;
    }

    open_list.push(start_jps_node);

    while (!open_list.empty())
    {
        GridNode *current_astar_node = open_list.top();
        open_list.pop();

        if (closed_set.count(current_astar_node))
        {
            continue;
        }
        closed_set.insert(current_astar_node);

        if (current_astar_node->need_sight)
        {
            if (has_line_of_sight(current_astar_node, &goal_cruise_pos_node, min_height, agent_id, constraints))
            {
                float los_cost = octile_distance(current_astar_node, &goal_cruise_pos_node);
                float final_t = current_astar_node->t + cruise_speed_t_ * los_cost;
                float final_g = current_astar_node->g + los_cost;

                auto goal_validity_at_final_t = is_valid_position(
                    goal_cruise_pos_node.y, goal_cruise_pos_node.x, goal_cruise_pos_node.z,
                    final_t, min_height, agent_id, constraints);

                if (goal_validity_at_final_t.first)
                {
                    GridNode *final_los_node = allocate_node(
                        goal_cruise_pos_node.y, goal_cruise_pos_node.x, goal_cruise_pos_node.z,
                        final_t, final_g, 0.0f, current_astar_node, current_astar_node->jump_step);
                    std::vector<GridNode *> path_ptrs;
                    GridNode *trace_back_node = final_los_node;
                    while (trace_back_node != nullptr)
                    {
                        path_ptrs.push_back(trace_back_node);
                        if (trace_back_node == start_jps_node)
                            break;
                        if (trace_back_node->parent == nullptr && trace_back_node != start_jps_node)
                            break;
                        trace_back_node = trace_back_node->parent;
                    }
                    std::reverse(path_ptrs.begin(), path_ptrs.end());
                    return {path_ptrs, std::nullopt};
                }
            }
        }

        if (std::fabs(current_astar_node->y - goal_cruise_pos_node.y) < 1e-6 &&
            std::fabs(current_astar_node->x - goal_cruise_pos_node.x) < 1e-6 &&
            std::fabs(current_astar_node->z - goal_cruise_pos_node.z) < 1e-6)
        {
            std::vector<GridNode *> path_ptrs;
            GridNode *trace_back_node = current_astar_node;
            while (trace_back_node != nullptr)
            {
                path_ptrs.push_back(trace_back_node);
                if (trace_back_node == start_jps_node)
                    break;
                if (trace_back_node->parent == nullptr && trace_back_node != start_jps_node)
                {
                    break;
                }
                trace_back_node = trace_back_node->parent;
            }
            std::reverse(path_ptrs.begin(), path_ptrs.end());
            return {path_ptrs, std::nullopt};
        }

        auto neighbor_segments = get_neighbors(current_astar_node, min_height, agent_id, constraints);

        std::priority_queue<std::pair<float, GridNode *>, std::vector<std::pair<float, GridNode *>>, std::greater<std::pair<float, GridNode *>>> temp_neighbor_pq;

        for (const auto &segment : neighbor_segments)
        {
            if (segment.empty())
                continue;
            GridNode *end_of_segment_node = segment.back();
            if (closed_set.count(end_of_segment_node))
                continue;

            end_of_segment_node->h = octile_distance(end_of_segment_node, &goal_cruise_pos_node);
            temp_neighbor_pq.push({end_of_segment_node->h, end_of_segment_node});
        }

        int neighbors_expanded = 0;
        while (!temp_neighbor_pq.empty() && neighbors_expanded < 3)
        {
            GridNode *segment_end_node_to_jump_from = temp_neighbor_pq.top().second;
            temp_neighbor_pq.pop();
            neighbors_expanded++;

            GridNode *jump_point_node = jump(
                segment_end_node_to_jump_from,
                current_astar_node,
                goal_cruise_pos_node,
                min_height, agent_id, constraints,
                current_astar_node->jump_step,
                5);

            if (jump_point_node)
            {
                if (closed_set.count(jump_point_node))
                    continue;

                jump_point_node->h = octile_distance(jump_point_node, &goal_cruise_pos_node);
                jump_point_node->f = jump_point_node->g + jump_point_node->h;

                if (jump_point_node->h < 20.0f)
                {
                    jump_point_node->jump_step = 1;
                }
                open_list.push(jump_point_node);
            }
        }
    }
    return {{}, "Cruise path not found."};
}

std::pair<std::vector<GridNode>, std::vector<int>> OptimizedPathFinder::extract_turning_points(
    const std::vector<GridNode *> &path_ptrs)
{
    std::vector<GridNode> turning_points_objs;
    std::vector<int> turning_indices;
    if (path_ptrs.size() <= 1)
    {
        for (GridNode *ptr : path_ptrs)
            if (ptr)
                turning_points_objs.push_back(*ptr);
        return {turning_points_objs, {}};
    }
    if (path_ptrs.size() == 2)
    {
        for (GridNode *ptr : path_ptrs)
            if (ptr)
                turning_points_objs.push_back(*ptr);
        return {turning_points_objs, {}};
    }

    if (path_ptrs[0])
        turning_points_objs.push_back(*path_ptrs[0]);

    for (size_t i = 1; i < path_ptrs.size() - 1; ++i)
    {
        GridNode *prev_node = path_ptrs[i - 1];
        GridNode *curr_node = path_ptrs[i];
        GridNode *next_node = path_ptrs[i + 1];

        if (!prev_node || !curr_node || !next_node)
            continue;

        float dy1 = curr_node->y - prev_node->y;
        float dx1 = curr_node->x - prev_node->x;
        float dz1 = curr_node->z - prev_node->z;

        float dy2 = next_node->y - curr_node->y;
        float dx2 = next_node->x - curr_node->x;
        float dz2 = next_node->z - curr_node->z;

        float norm1_sq = dy1 * dy1 + dx1 * dx1 + dz1 * dz1;
        float norm2_sq = dy2 * dy2 + dx2 * dx2 + dz2 * dz2;

        bool is_turn = false;
        if (norm1_sq < 1e-9 || norm2_sq < 1e-9)
        {
            if (!(norm1_sq < 1e-9 && norm2_sq < 1e-9))
                is_turn = true;
        }
        else
        {
            float dot_product = dy1 * dy2 + dx1 * dx2 + dz1 * dz2;
            float cos_theta = dot_product / (std::sqrt(norm1_sq) * std::sqrt(norm2_sq));
            if (cos_theta < 0.999f)
            {
                is_turn = true;
            }
        }

        if (is_turn)
        {
            turning_points_objs.push_back(*curr_node);
            turning_indices.push_back(static_cast<int>(i));
        }
    }
    if (path_ptrs.back())
        turning_points_objs.push_back(*path_ptrs.back());
    return {turning_points_objs, turning_indices};
}

std::pair<std::vector<GridNode>, std::vector<int>> OptimizedPathFinder::extract_turning_points_from_nodes(
    const std::vector<GridNode> &path_nodes)
{
    std::vector<GridNode> turning_points_objs;
    std::vector<int> turning_indices;
    if (path_nodes.size() <= 1)
    {
        turning_points_objs = path_nodes;
        return {turning_points_objs, {}};
    }
    if (path_nodes.size() == 2)
    {
        turning_points_objs = path_nodes;
        return {turning_points_objs, {}};
    }

    turning_points_objs.push_back(path_nodes[0]);

    for (size_t i = 1; i < path_nodes.size() - 1; ++i)
    {
        const GridNode &prev_node = path_nodes[i - 1];
        const GridNode &curr_node = path_nodes[i];
        const GridNode &next_node = path_nodes[i + 1];

        float dy1 = curr_node.y - prev_node.y;
        float dx1 = curr_node.x - prev_node.x;
        float dz1 = curr_node.z - prev_node.z;

        float dy2 = next_node.y - curr_node.y;
        float dx2 = next_node.x - curr_node.x;
        float dz2 = next_node.z - curr_node.z;

        float norm1_sq = dy1 * dy1 + dx1 * dx1 + dz1 * dz1;
        float norm2_sq = dy2 * dy2 + dx2 * dx2 + dz2 * dz2;

        bool is_turn = false;
        if (norm1_sq < 1e-9 || norm2_sq < 1e-9)
        {
            if (!(norm1_sq < 1e-9 && norm2_sq < 1e-9))
                is_turn = true;
        }
        else
        {
            float dot_product = dy1 * dy2 + dx1 * dx2 + dz1 * dz2;
            float cos_theta = dot_product / (std::sqrt(norm1_sq) * std::sqrt(norm2_sq));
            if (cos_theta < 0.999f)
            {
                is_turn = true;
            }
        }

        if (is_turn)
        {
            turning_points_objs.push_back(curr_node);
            turning_indices.push_back(static_cast<int>(i));
        }
    }
    turning_points_objs.push_back(path_nodes.back());
    return {turning_points_objs, turning_indices};
}

float OptimizedPathFinder::manhattan_distance(const GridNode *n1, const GridNode *n2) const
{
    if (!n1 || !n2)
        return std::numeric_limits<float>::max();
    return std::fabs(n1->y - n2->y) + std::fabs(n1->x - n2->x) + std::fabs(n1->z - n2->z);
}

float OptimizedPathFinder::octile_distance(const GridNode *n1, const GridNode *n2) const
{
    if (!n1 || !n2)
        return std::numeric_limits<float>::max();
    float dy = std::fabs(n1->y - n2->y);
    float dx = std::fabs(n1->x - n2->x);
    float dz = std::fabs(n1->z - n2->z);
    float diag_xy = std::min(dx, dy);
    float straight_xy = std::max(dx, dy) - diag_xy;
    return diag_xy * 1.41421356f + straight_xy + dz;
}

float OptimizedPathFinder::euclidean_distance_sq(const GridNode *n1, const GridNode *n2) const
{
    if (!n1 || !n2)
        return std::numeric_limits<float>::max();
    float dy = n1->y - n2->y;
    float dx = n1->x - n2->x;
    float dz = n1->z - n2->z;
    return dy * dy + dx * dx + dz * dz;
}
