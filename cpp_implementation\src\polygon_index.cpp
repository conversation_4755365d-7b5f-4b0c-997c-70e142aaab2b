#include "polygon_index.h" // Should be "../include/polygon_index.h"

namespace CVToolbox
{

    void PolygonIndexCpp::add_polygon(const std::string &polygon_id, const Polygon2D &vertices)
    {
        if (vertices.empty())
        {
            // Optionally log a warning or throw an error for empty polygons
            return;
        }
        polygons_[polygon_id] = vertices;
        bounds_[polygon_id] = BoundingBox2D(vertices); // BoundingBox2D constructor computes bounds
    }

    bool PolygonIndexCpp::remove_polygon(const std::string &polygon_id)
    {
        bool found = false;
        if (polygons_.count(polygon_id))
        {
            polygons_.erase(polygon_id);
            found = true;
        }
        if (bounds_.count(polygon_id))
        {
            bounds_.erase(polygon_id);
            // found should ideally be true if it was in polygons_
        }
        return found; // Returns true if it was in polygons_ map
    }

    bool PolygonIndexCpp::get_polygon_vertices(const std::string &polygon_id, Polygon2D &out_vertices) const
    {
        auto it = polygons_.find(polygon_id);
        if (it != polygons_.end())
        {
            out_vertices = it->second;
            return true;
        }
        return false;
    }

    bool PolygonIndexCpp::get_polygon_bounds(const std::string &polygon_id, BoundingBox2D &out_bounds) const
    {
        auto it = bounds_.find(polygon_id);
        if (it != bounds_.end())
        {
            out_bounds = it->second;
            return true;
        }
        return false;
    }

    std::vector<std::string> PolygonIndexCpp::get_all_polygon_ids() const
    {
        std::vector<std::string> ids;
        ids.reserve(polygons_.size());
        for (const auto &pair : polygons_)
        {
            ids.push_back(pair.first);
        }
        return ids;
    }

    std::vector<std::string> PolygonIndexCpp::get_candidate_polygons(const Point2D &point) const
    {
        std::vector<std::string> candidates;
        for (const auto &pair : bounds_)
        {
            if (pair.second.contains(point))
            {
                candidates.push_back(pair.first);
            }
        }
        return candidates;
    }

    void PolygonIndexCpp::clear()
    {
        polygons_.clear();
        bounds_.clear();
    }

    bool PolygonIndexCpp::has_polygon(const std::string &polygon_id) const
    {
        return polygons_.count(polygon_id) > 0;
    }

} // namespace CVToolbox
