#ifndef OPTIMIZED_PATH_FINDER_H
#define OPTIMIZED_PATH_FINDER_H

#include "grid_node.h"
#include "occupancy_map.h"
#include "lru_cache.h"
#include "grid_node_data.h" // For Constraint

#include <vector>
#include <string>
#include <optional>
#include <set>
#include <queue>
#include <unordered_set>
#include <unordered_map>
#include <array> // For std::array, if DIRECTION_PRIORITIES becomes std::array

class OptimizedPathFinder
{
public:
    struct Direction
    {
        float dy, dx, dz;
    };

    OptimizedPathFinder(
        const OccupancyMap &map_ref,
        double takeoff_speed,
        double cruise_speed,
        double landing_speed,
        bool only_turning_points,
        int max_jps_steps,
        bool need_smooth,
        double smoothness_factor,
        int cache_size = 10000);

    std::pair<std::pair<std::vector<GridNode>, std::vector<GridNode>>, std::optional<std::string>>
    find_path(
        const GridNode &start_node,
        const GridNode &goal_node,
        int min_height,
        const std::string &agent_id,
        const std::vector<Constraint> &constraints);

private:
    const OccupancyMap &map_;
    double takeoff_speed_t_;
    double cruise_speed_t_;
    double landing_speed_t_;
    bool only_turning_points_;
    int max_jps_steps_;
    bool need_smooth_;
    double smoothness_factor_;

    // Node pool for memory optimization
    std::vector<GridNode> node_pool_;
    size_t next_node_idx_;
    GridNode *allocate_node(float y, float x, float z, float t, float g, float h, GridNode *parent, int jump_step, bool need_sight = false);
    void reset_node_pool();

    // Caches
    mutable LRUCache<std::tuple<float, float, float, float>, std::pair<bool, std::optional<std::string>>> constraint_cache_;
    using StaticNeighborCacheKey = std::tuple<float, float, float, int>; // y, x, z, jump_step
    using StaticNeighborCacheValue = std::vector<std::vector<std::tuple<float, float, float>>>;
    mutable LRUCache<StaticNeighborCacheKey, StaticNeighborCacheValue> static_neighbor_cache_;

    // Helper methods
    float cost_between_coords(float y1, float x1, float z1, float y2, float x2, float z2) const;
    float manhattan_distance(const GridNode *n1, const GridNode *n2) const;
    float octile_distance(const GridNode *n1, const GridNode *n2) const;
    float euclidean_distance_sq(const GridNode *n1, const GridNode *n2) const;

    std::pair<bool, std::optional<std::string>> is_valid_position(
        float y, float x, float z, float t,
        int min_height,
        const std::string &agent_id,
        const std::vector<Constraint> &constraints,
        bool ignore_min_height = false);

    std::pair<bool, std::optional<std::string>> check_constraints_and_collisions(
        const GridNode &node,
        const std::string &agent_id,
        const std::vector<Constraint> &constraints);

    std::vector<GridNode> reconstruct_path_objects(GridNode *target_node_ptr);

    std::pair<std::vector<GridNode *>, std::optional<std::string>> vertical_takeoff(
        const GridNode &start_pos_node,
        int min_height,
        const std::string &agent_id,
        const std::vector<Constraint> &constraints);

    std::pair<std::vector<GridNode *>, std::optional<std::string>> vertical_landing(
        GridNode *last_cruise_node,
        const GridNode &goal_pos_node,
        const std::string &agent_id,
        const std::vector<Constraint> &constraints);

    std::pair<std::vector<GridNode *>, std::optional<std::string>> find_cruise_path(
        GridNode *start_jps_node,
        const GridNode &goal_cruise_pos_node,
        int min_height,
        const std::string &agent_id,
        const std::vector<Constraint> &constraints);

    std::vector<std::vector<GridNode *>> get_neighbors(
        GridNode *current_node_ptr,
        int min_height,
        const std::string &agent_id,
        const std::vector<Constraint> &constraints,
        int vertical_look_steps = 5);

    GridNode *jump(
        GridNode *current_node_in_path,
        GridNode *parent_for_direction,
        const GridNode &goal_node,
        int min_height,
        const std::string &agent_id,
        const std::vector<Constraint> &constraints,
        int jump_step_val,
        int vertical_look_steps = 5);

    bool has_line_of_sight(
        const GridNode *p1,
        const GridNode *p2,
        int min_height,
        const std::string &agent_id,
        const std::vector<Constraint> &constraints);

    std::pair<std::vector<GridNode>, std::vector<int>> extract_turning_points(
        const std::vector<GridNode *> &path_ptrs);

    std::vector<GridNode> smooth_path(
        const std::vector<GridNode> &path,
        int min_height,
        const std::string &agent_id,
        const std::vector<Constraint> &constraints,
        double smoothness_factor);

    std::pair<std::vector<GridNode>, std::vector<int>> extract_turning_points_from_nodes(
        const std::vector<GridNode> &path_nodes);

    // Helper structs for priority queue in find_cruise_path (defined in .cpp or here if needed by other classes)
    struct CompareGridNodePtrs
    {
        bool operator()(const GridNode *a, const GridNode *b) const
        {
            if (!a || !b)
                return false;   // Or handle error appropriately
            return a->f > b->f; // Min-heap based on f value
        }
    };

    struct GridNodePtrHasher
    {
        std::size_t operator()(const GridNode *node) const
        {
            if (!node)
                return 0; // Or handle error appropriately
            // Simple hash combining coordinates, adjust if collisions are too frequent
            std::size_t h1 = std::hash<float>{}(node->y);
            std::size_t h2 = std::hash<float>{}(node->x);
            std::size_t h3 = std::hash<float>{}(node->z);
            return h1 ^ (h2 << 1) ^ (h3 << 2);
        }
    };

    struct GridNodePtrEqualTo
    {
        bool operator()(const GridNode *lhs, const GridNode *rhs) const
        {
            if (lhs == rhs)
                return true;
            if (!lhs || !rhs)
                return false;
            return std::fabs(lhs->y - rhs->y) < 1e-6 &&
                   std::fabs(lhs->x - rhs->x) < 1e-6 &&
                   std::fabs(lhs->z - rhs->z) < 1e-6;
            // Note: Time is not included for closed set uniqueness in this JPS variant
        }
    };

    static const std::vector<Direction> DIRECTION_PRIORITIES;
};

#endif // OPTIMIZED_PATH_FINDER_H
