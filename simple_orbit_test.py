#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的轨道路径规划算法测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 创建一个模拟的地图类用于测试
class MockMap3D:
    def __init__(self):
        self.height = 1000
        self.width = 1000
        self.depth = 100
        
        # 模拟障碍物管理器
        self.obstacle_manager = MockObstacleManager()
        
        # 模拟网格转换器
        self.grid_converter = MockGridConverter()
        
        # 模拟不可通行点集合
        self.non_traversable = set()
        
        # 添加一些测试障碍物
        for y in range(400, 450):
            for x in range(400, 450):
                for z in range(0, 50):
                    self.non_traversable.add((y, x, z))
    
    def traversable(self, y, x, z):
        return (y, x, z) not in self.non_traversable

class MockObstacleManager:
    def __init__(self):
        self.orbit_paths = {}
        self.orbit_quadrants = {}
        
        # 创建一个测试轨道
        self.orbit_paths["test_zone"] = [
            (390, 390), (390, 400), (390, 410), (400, 420), (410, 420),
            (420, 410), (420, 400), (420, 390), (410, 380), (400, 380)
        ]
        
        # 创建象限分类
        self.orbit_quadrants["test_zone"] = {
            "N": {0, 1},
            "NE": {2},
            "E": {3, 4},
            "SE": {5},
            "S": {6, 7},
            "SW": {8},
            "W": {9},
            "NW": {0}
        }
    
    def get_type_at_position(self, pos):
        # 模拟在特定区域返回禁飞区类型
        y, x, z = pos
        if 400 <= y < 450 and 400 <= x < 450:
            return ["test_zone"]
        return []
    
    def get_orbit_path(self, zone_name):
        return self.orbit_paths.get(zone_name, [])
    
    def get_orbit_quadrants(self, zone_name):
        return self.orbit_quadrants.get(zone_name, {})

class MockGridConverter:
    def relative_to_geo(self, y, x, z):
        return {"lat": 39.0 + y * 0.001, "lon": 116.0 + x * 0.001, "alt": z * 10}

def test_orbit_pathfinder():
    """测试轨道路径规划器的基本功能"""
    print("开始测试轨道路径规划器...")
    
    # 导入轨道路径规划器
    try:
        from src.core.pathfinding.orbit import OrbitPathFinder
        print("✓ 成功导入 OrbitPathFinder")
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    
    # 创建模拟地图
    mock_map = MockMap3D()
    print("✓ 创建模拟地图")
    
    # 创建轨道路径规划器
    try:
        orbit_finder = OrbitPathFinder(mock_map)
        print("✓ 成功创建 OrbitPathFinder 实例")
    except Exception as e:
        print(f"✗ 创建实例失败: {e}")
        return False
    
    # 测试基本方法
    print("\n测试基本方法:")
    
    # 测试位置有效性检查
    try:
        is_valid, error = orbit_finder._is_valid_position(
            (100, 100, 50), 0, 40, "test", None, None
        )
        print(f"✓ 位置有效性检查: {is_valid}")
    except Exception as e:
        print(f"✗ 位置有效性检查失败: {e}")
    
    # 测试获取障碍物
    try:
        obstacle = orbit_finder._get_obstacle_at_position((425, 425, 25))
        print(f"✓ 获取障碍物: {obstacle}")
    except Exception as e:
        print(f"✗ 获取障碍物失败: {e}")
    
    # 测试搜索轨道点
    try:
        nearby_points = orbit_finder._find_nearby_orbit_points((400, 400, 50), "test_zone")
        print(f"✓ 搜索轨道点: 找到 {len(nearby_points)} 个点")
    except Exception as e:
        print(f"✗ 搜索轨道点失败: {e}")
    
    # 测试确定目标象限
    try:
        target_quadrants = orbit_finder._determine_target_quadrants((500, 500, 50), "test_zone")
        print(f"✓ 确定目标象限: {target_quadrants}")
    except Exception as e:
        print(f"✗ 确定目标象限失败: {e}")
    
    # 测试选择轨道方向
    try:
        direction = orbit_finder._choose_orbit_direction(0, ["E", "SE"], "test_zone", (500, 500, 50))
        print(f"✓ 选择轨道方向: {'顺时针' if direction == 1 else '逆时针'}")
    except Exception as e:
        print(f"✗ 选择轨道方向失败: {e}")
    
    # 测试直线移动
    try:
        next_pos, next_time, obstacle_zone = orbit_finder._direct_move_towards_goal(
            (300, 300, 50), (500, 500, 50), 0, 40, "test", None, None
        )
        if next_pos:
            print(f"✓ 直线移动成功: {next_pos}")
        else:
            print(f"✓ 直线移动遇到障碍: {obstacle_zone}")
    except Exception as e:
        print(f"✗ 直线移动测试失败: {e}")
    
    print("\n基本功能测试完成!")
    return True

def test_simple_pathfinding():
    """测试简单的路径规划"""
    print("\n开始测试简单路径规划...")
    
    try:
        from src.core.pathfinding.orbit import OrbitPathFinder
        mock_map = MockMap3D()
        orbit_finder = OrbitPathFinder(mock_map)
        
        # 测试起飞阶段
        print("测试起飞阶段...")
        takeoff_path, error = orbit_finder._vertical_takeoff(
            (100, 100, 5), 50, "test", 0, None, None
        )
        if takeoff_path:
            print(f"✓ 起飞成功: {len(takeoff_path)} 个节点")
        else:
            print(f"✗ 起飞失败: {error}")
        
        # 测试降落阶段
        if takeoff_path:
            print("测试降落阶段...")
            from src.core.node_3d import GridNode3D
            last_node = GridNode3D(200, 200, 50, 100)
            landing_path, error = orbit_finder._vertical_landing(
                last_node, (200, 200, 5), "test", None, None
            )
            if landing_path:
                print(f"✓ 降落成功: {len(landing_path)} 个节点")
            else:
                print(f"✗ 降落失败: {error}")
        
        print("简单路径规划测试完成!")
        return True
        
    except Exception as e:
        print(f"✗ 简单路径规划测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("轨道路径规划算法简单测试")
    print("=" * 50)
    
    # 测试基本功能
    success1 = test_orbit_pathfinder()
    
    # 测试简单路径规划
    success2 = test_simple_pathfinding()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✓ 所有测试通过！轨道路径规划算法基本功能正常。")
    else:
        print("✗ 部分测试失败，请检查代码。")
    print("=" * 50)

if __name__ == "__main__":
    main()
