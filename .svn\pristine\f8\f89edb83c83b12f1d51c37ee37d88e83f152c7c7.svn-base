0000000000000000000000000000000000000000 d129566186a99b5899fd25fdda37ff821e3ca7cd liuchang1230 <<EMAIL>> 1738587021 +0800	branch: Created from HEAD
d129566186a99b5899fd25fdda37ff821e3ca7cd 19a5b8ec7dd68bd93bd18c9d4d0f831da8a92b7e liuchang1230 <<EMAIL>> 1738587064 +0800	commit: feat: Add Jump Point Search implementation for 3D path planning
19a5b8ec7dd68bd93bd18c9d4d0f831da8a92b7e 7c6028ccad76c1f0a264552fcf854940dd9a5d1e liuchang1230 <<EMAIL>> 1738665875 +0800	commit: feat: Complete JPS 3D implementation with performance benchmarks\n- Add optimized neighbor pruning in 3D space\n- Update path validation logic\n- Add performance test cases
7c6028ccad76c1f0a264552fcf854940dd9a5d1e 3697c621af8abf9ab7041d03407fc6c7833e2f7e liuchang1230 <<EMAIL>> 1738721460 +0800	commit: feat: Complete JPS 3D implementation with performance benchmarks\n- Add optimized neighbor pruning in 3D space\n- Update path validation logic\n- Add performance test cases
3697c621af8abf9ab7041d03407fc6c7833e2f7e f3e491ebaf5f4d6f4300ca91ce580ce54a939ae8 liuchang1230 <<EMAIL>> 1738755327 +0800	commit: feat: Complete JPS 3D implementation with performance benchmarks\n- Add optimized neighbor pruning in 3D space\n- Update path validation logic\n- Add performance test cases
f3e491ebaf5f4d6f4300ca91ce580ce54a939ae8 e817b55a9cf40d25ce97a12eb795499982f430ad liuchang1230 <<EMAIL>> 1739175746 +0800	commit: 增加了对角移动的支持
e817b55a9cf40d25ce97a12eb795499982f430ad 78bd0dc68ed07cd5abef1f33a2e2a9d262754b11 liuchang1230 <<EMAIL>> 1740103351 +0800	commit (amend): 增加了对角移动的支持
