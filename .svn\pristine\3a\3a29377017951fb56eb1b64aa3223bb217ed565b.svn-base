{"file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/test_polygon_buffer_simple.py": {"language": "Python", "code": 129, "comment": 45, "blank": 38}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/main.py": {"language": "Python", "code": 64, "comment": 11, "blank": 16}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/test_polygon_buffer.py": {"language": "Python", "code": 31, "comment": 12, "blank": 12}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/test_complex_polygon.py": {"language": "Python", "code": 105, "comment": 35, "blank": 31}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/README.md": {"language": "<PERSON><PERSON>", "code": 107, "comment": 0, "blank": 22}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/utils/logging.py": {"language": "Python", "code": 32, "comment": 5, "blank": 12}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/requirements.txt": {"language": "pip requirements", "code": 8, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/PACKAGE_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 113, "comment": 0, "blank": 26}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/utils/exceptions.py": {"language": "Python", "code": 18, "comment": 0, "blank": 17}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/utils/visualization.py": {"language": "Python", "code": 168, "comment": 22, "blank": 40}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/validation/route_validator.py": {"language": "Python", "code": 256, "comment": 54, "blank": 51}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/tests/visualization_3d.py": {"language": "Python", "code": 287, "comment": 53, "blank": 58}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/utils/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/tests/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/validation/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/tests/test_producer.py": {"language": "Python", "code": 208, "comment": 33, "blank": 31}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/tests/test_3d.py": {"language": "Python", "code": 416, "comment": 147, "blank": 132}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/pathfinding/astar.py": {"language": "Python", "code": 238, "comment": 24, "blank": 48}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/node_3d.py": {"language": "Python", "code": 130, "comment": 7, "blank": 28}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/pathfinding/high_level_policy_3d.py": {"language": "Python", "code": 168, "comment": 48, "blank": 41}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/pathfinding/__init__.py": {"language": "Python", "code": 1, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/pathfinding/astar_v2.py": {"language": "Python", "code": 744, "comment": 181, "blank": 155}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/pathfinding/jps_v4.py": {"language": "Python", "code": 969, "comment": 189, "blank": 196}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/pathfinding/jps_v3.py": {"language": "Python", "code": 972, "comment": 208, "blank": 197}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/map/grid_converter.py": {"language": "Python", "code": 522, "comment": 83, "blank": 109}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/pathfinding/jps_v2_v1.py": {"language": "Python", "code": 948, "comment": 192, "blank": 191}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/config/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/pathfinding/jps_v2.py": {"language": "Python", "code": 922, "comment": 198, "blank": 188}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/pathfinding/jps.py": {"language": "Python", "code": 788, "comment": 172, "blank": 155}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/map/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/map/optimized_polygon_check.py": {"language": "Python", "code": 202, "comment": 63, "blank": 77}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/risk_assessment_consumer.py": {"language": "Python", "code": 460, "comment": 72, "blank": 84}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/map/occupancy_map.py": {"language": "Python", "code": 256, "comment": 23, "blank": 38}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/kafka_consumer.py": {"language": "Python", "code": 207, "comment": 14, "blank": 33}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/map/obstacle_manager.py": {"language": "Python", "code": 157, "comment": 10, "blank": 38}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/message_handlers/reroute_handler.py": {"language": "Python", "code": 131, "comment": 33, "blank": 34}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/message_handlers/route_handler.py": {"language": "Python", "code": 111, "comment": 20, "blank": 18}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/map/map_handler_3d.py": {"language": "Python", "code": 695, "comment": 163, "blank": 175}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/message_handlers/state_handler.py": {"language": "Python", "code": 38, "comment": 5, "blank": 13}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/message_handlers/planning_handler.py": {"language": "Python", "code": 188, "comment": 53, "blank": 36}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/map/cache/shaoquan/grid_mappings.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/core/map/cache/chongqing/grid_mappings.json": {"language": "JSON", "code": 18, "comment": 0, "blank": 0}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/message_handlers/base.py": {"language": "Python", "code": 378, "comment": 38, "blank": 63}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/message_handlers/approval_handler.py": {"language": "Python", "code": 77, "comment": 22, "blank": 17}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/config/settings.py": {"language": "Python", "code": 154, "comment": 12, "blank": 38}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/message_handlers/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/handlers/message_handlers/no_fly_zone_handler.py": {"language": "Python", "code": 182, "comment": 74, "blank": 38}, "file:///d%3A/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4/src/tests/fixed_routes.json": {"language": "JSON", "code": 145, "comment": 0, "blank": 0}}