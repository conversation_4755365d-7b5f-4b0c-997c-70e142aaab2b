#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试浮点数坐标功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import math

def test_basic_functionality():
    """测试基本功能"""
    print("开始基本功能测试...")
    
    try:
        # 导入轨道路径规划器
        from src.core.pathfinding.orbit import OrbitPathFinder
        print("✓ 成功导入 OrbitPathFinder")
        
        # 创建一个最简单的模拟地图
        class SimpleMap:
            def __init__(self):
                self.height = 100
                self.width = 100
                self.depth = 10
                self.obstacle_manager = SimpleObstacleManager()
                self.grid_converter = SimpleGridConverter()
            
            def traversable(self, y, x, z):
                return True  # 所有位置都可通行
        
        class SimpleObstacleManager:
            def get_type_at_position(self, pos):
                return []
            def get_orbit_path(self, zone_name):
                return []
            def get_orbit_quadrants(self, zone_name):
                return {}
        
        class SimpleGridConverter:
            def relative_to_geo(self, y, x, z):
                return {"lat": 39.0, "lon": 116.0, "alt": z * 10}
        
        # 创建地图和路径规划器
        simple_map = SimpleMap()
        orbit_finder = OrbitPathFinder(simple_map)
        print("✓ 成功创建 OrbitPathFinder 实例")
        
        # 测试直线移动函数
        print("\n测试直线移动函数...")
        current_pos = (10.0, 10.0, 5.0)
        goal = (20.0, 30.0, 5.0)
        
        next_pos, next_time, obstacle_zone = orbit_finder._direct_move_towards_goal(
            current_pos, goal, 0, 5, "test", None, None, 1.0
        )
        
        if next_pos:
            print(f"✓ 直线移动成功")
            print(f"  当前位置: {current_pos}")
            print(f"  目标位置: {goal}")
            print(f"  下一位置: ({next_pos[0]:.3f}, {next_pos[1]:.3f}, {next_pos[2]:.3f})")
            
            # 验证移动方向
            dy = next_pos[0] - current_pos[0]
            dx = next_pos[1] - current_pos[1]
            goal_dy = goal[0] - current_pos[0]
            goal_dx = goal[1] - current_pos[1]
            
            # 计算角度
            move_angle = math.degrees(math.atan2(dy, dx))
            goal_angle = math.degrees(math.atan2(goal_dy, goal_dx))
            
            print(f"  移动角度: {move_angle:.2f}°")
            print(f"  目标角度: {goal_angle:.2f}°")
            print(f"  角度误差: {abs(move_angle - goal_angle):.3f}°")
            
            if abs(move_angle - goal_angle) < 1.0:
                print("  ✓ 移动方向正确")
            else:
                print("  ⚠ 移动方向可能有偏差")
        else:
            print(f"✗ 直线移动失败: {obstacle_zone}")
        
        # 测试简单路径规划
        print("\n测试简单路径规划...")
        start = (5, 5, 5)
        goal = (15, 25, 5)
        
        path, error = orbit_finder.find_path(
            start=start,
            goal=goal,
            min_height=5,
            agent_id="test",
            start_time=0
        )
        
        if path:
            print(f"✓ 路径规划成功，路径长度: {len(path)}")
            print(f"  起点: ({path[0].y}, {path[0].x}, {path[0].z})")
            print(f"  终点: ({path[-1].y}, {path[-1].x}, {path[-1].z})")
            
            # 计算路径效率
            direct_distance = math.sqrt((goal[0] - start[0])**2 + (goal[1] - start[1])**2)
            actual_distance = 0
            for i in range(1, len(path)):
                actual_distance += math.sqrt(
                    (path[i].y - path[i-1].y)**2 + (path[i].x - path[i-1].x)**2
                )
            
            efficiency = (direct_distance / actual_distance) * 100 if actual_distance > 0 else 0
            print(f"  路径效率: {efficiency:.1f}%")
            
            if efficiency > 90:
                print("  ✓ 路径效率很高")
            else:
                print("  ⚠ 路径效率可以改进")
        else:
            print(f"✗ 路径规划失败: {error}")
        
        print("\n✓ 所有基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 40)
    print("浮点数坐标功能最小化测试")
    print("=" * 40)
    
    success = test_basic_functionality()
    
    print("\n" + "=" * 40)
    if success:
        print("✓ 测试成功！浮点数坐标功能正常工作。")
    else:
        print("✗ 测试失败！")
    print("=" * 40)

if __name__ == "__main__":
    main()
