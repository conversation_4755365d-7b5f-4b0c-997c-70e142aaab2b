# 3D路径规划服务

本项目是一个基于Kafka的3D路径规划服务，提供无人机航线规划、验证和禁飞区管理功能。

## 项目结构

## 系统架构
系统主要包含以下模块：
- `kafka_consumer.py`: Kafka 消息处理模块
- `high_level_policy_3d.py`: CBS 算法实现， 冲突算法
- `low_level_policy_3d.py`: A* 路径搜索
- `grid_converter.py`: 坐标转换处理
- `map_handler_3d.py`: 三维地图管理

```
project/
├── src/                     # 源代码目录
│   ├── core/               # 核心功能
│   │   ├── map/           # 地图相关（地图处理、坐标转换）
│   │   ├── pathfinding/   # 路径规划算法
│   │   └── validation/    # 路径验证和占用图
│   ├── handlers/          # 消息处理
│   │   └── message_handlers/  # 各类消息处理器
│   ├── utils/             # 工具类
│   └── config/            # 配置管理
└── main.py                # 服务入口
```

## 功能特点

- 3D路径规划和验证
  - 支持自定义起点和终点
  - 考虑最小巡航高度
  - 路径冲突检测

- 实时禁飞区管理
  - 动态添加圆柱形禁飞区
  - 已规划路径的冲突检测
  - 受影响航线的重规划

- 固定航线验证
  - 验证已存在的航线
  - 检查与其他航线的冲突

## 配置说明

1. 配置文件位置：
   - 默认位置：与可执行文件同一目录下的 `config.json`
   - 可通过 `--config` 命令行参数指定其他位置

2. 配置文件内容：
```json
{
    "location": "nanjing",                 // 地点名称，可通过 --location 参数覆盖
    "server_address": "***********",       // 服务器地址，可通过 --server 参数覆盖
    "database": {
        "host": "{server_address}",        // 使用 server_address 的值
        "port": 3306,
        "database": "siteinspectiondb",
        "user": "root",
        "password": "root",
        "use_unicode": true,
        "get_warnings": true,
        "charset": "utf8mb4"
    },
    "kafka": {
        "bootstrap_servers": ["{server_address}:9093"],
        "request_topic": "flight_route_apply",
        "response_topic": "flight_route_apply_reply",
        // 其他 Kafka 配置...
    },
    "map_profiles": {
        "nanjing": {
            // 南京地图配置...
        },
        "shijiazhuang": {
            // 石家庄地图配置...
        }
    },
    // 其他配置...
}
```

3. 命令行参数：
```bash
# 指定配置文件路径
./route_planning_service --config /path/to/your/config.json

# 指定地点和服务器地址
./route_planning_service --location shijiazhuang --server ***********

# 组合使用参数
./route_planning_service --config /path/to/your/config.json --location shijiazhuang
```

## 运行服务

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 启动服务：
```bash
# 使用默认配置
python main.py

# 指定运行模式
python main.py --mode normal

# 指定地点和服务器地址
python main.py --location shijiazhuang --server ***********

# 指定配置文件路径
python main.py --config /path/to/your/config.json
```

3. 打包服务：
```bash
# 确保在正确的conda环境中
conda activate forpyinstall

# 执行打包命令（不包含config.json）
pyinstaller --name route_planning_service --onefile --hidden-import=kafka --hidden-import=redis --hidden-import=numpy --collect-all numpy --collect-all matplotlib main.py
```

4. 部署服务：
```bash
# 将可执行文件和config.json放在同一目录下
cp config.json dist/
cd dist

# 运行服务
./route_planning_service
```

## 消息格式

1. 路径规划请求：
```json
{
    "bid": "请求ID",
    "flight_id": "航班ID",
    "take_off_point": "lon,lat,alt",
    "landing_point": "lon,lat,alt",
    "fly_height": "最小巡航高度",
    "begin_time": "YYYY-MM-DD HH:MM:SS"
}
```

2. 禁飞区请求：
```json
{
    "center": {
        "lat": 纬度,
        "lon": 经度
    },
    "radius": 半径（米）,
    "zone_name": "禁飞区名称"
}
```

## 开发说明

### 路径规划参数
- 最小飞行高度：由请求消息中的 `fly_height` 指定
- 开始时间：由请求消息中的 `begin_time` 指定
- 路径点序号：从 0 开始的整数序列

## 使用说明
1. 确保 Kafka 服务器可访问
2. 运行消费者程序：
```bash
python kafka_consumer.py
```
3. 发送路径规划请求到指定主题
4. 从响应主题接收规划结果

## 注意事项
1. 确保输入坐标格式正确
2. 最小飞行高度必须为正数
3. 时间格式必须为 "YYYY-MM-DD HH:MM:SS"
4. 所有高度值单位为米

## 开发特性
- 使用 Python 类型提示
- 统一的异常处理
- 结构化的日志记录
- 模块化的消息处理器设计

## 更新日志

### 2025-04-28
- **重大改进**：使用集合（Set）替代NumPy矩阵存储障碍物点，显著降低内存占用
  - 解决了大型地图和复杂禁飞区导致的内存溢出问题
  - 优化了障碍物点的存储和查询方式
- 优化禁飞区处理性能
  - 使用NumPy向量化操作替代嵌套循环，提高计算效率
  - 区分"安全缓冲区距离"（expand_distance）和"边界厚度"（boundary_thickness）两个概念
  - 从.env文件读取边界厚度和安全缓冲区距离配置
  - 优化圆柱体和多边形边界提取算法
  - 直接使用NumPy数组操作处理中间计算，减少内存转换开销
  - 仅生成禁飞区边界点而非整个体积，大幅减少点数量

### 2025-04-29
- **新增功能**：在路径规划前增加起点和终点检查，防止在禁飞区内部启动或结束规划。
  - `Map3D` 现在存储禁飞区的 2D 几何定义（圆心/半径、多边形顶点）。
  - `PlanningMessageHandler` 在调用规划器前使用 `Map3D.is_point_inside_any_nfz_2d` 检查起点和终点。
  - 如果起点或终点在禁飞区内，规划将中止并返回错误信息。
- **新增功能**：添加路径平滑功能（移动平均法）。
  - 在 `jps_v4.py` 中实现 `moving_average_smooth` 方法。
  - 通过 `.env` 文件中的 `NEED_SMOOTH` (布尔值) 和 `SMOOTHNESS` (浮点数) 控制是否启用及平滑程度。
  - 在 `find_path` 方法中，巡航阶段结束后根据配置调用平滑函数。

### 2025-04-30
*   **重构**:
    *   将项目配置从 `.env` 文件迁移至 `config.json` 文件，实现更结构化的配置管理。
    *   将原 `grid_mappings.json` 中的地图网格映射配置合并到 `config.json` 中，统一配置来源。
*   **Bug修复**: 相应地更新了代码库中所有模块对配置项的访问方式，解决了因配置迁移和合并导致的 `AttributeError`。

### 2025-05-09
*   **打包优化**:
    *   修改打包命令，移除 `--add-data "config.json:."` 参数，确保 `config.json` 不被打包到可执行文件中。
    *   更新 `route_planning_service.spec` 文件，移除对 `grid_mappings.json` 的引用。
*   **配置加载增强**:
    *   修改 `Settings` 类，使其能够从多个位置查找配置文件，提高配置文件加载的灵活性。
    *   添加命令行参数支持，允许通过命令行指定关键配置参数：
        *   `--config`: 指定配置文件路径
        *   `--location`: 指定地点（如 nanjing, shijiazhuang）
        *   `--server`: 指定服务器地址
    *   命令行参数的优先级高于配置文件中的设置，提供更灵活的配置方式。
*   **部署文档更新**:
    *   更新 `deploy.md`，添加关于配置文件处理和命令行参数的说明。
    *   更新 `server_deploy.md`，提供多种 systemd 服务配置选项，支持不同的配置方式。
    *   添加命令行参数使用示例和注意事项。

## 联系方式
如有问题或建议，请联系：<EMAIL>
