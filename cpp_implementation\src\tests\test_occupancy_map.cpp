#include "occupancy_map.h"
#include "grid_node_data.h" // For GridNodeData
#include "time_interval.h"  // For TimeInterval (used in printOptionalTimeInterval if needed, and by OccupancyMap)
#include <iostream>
#include <vector>
#include <string>
#include <array>
#include <optional>
#include <tuple>
// #include <memory> // No longer needed for std::make_shared<GridNode>

// Helper function to print TimeInterval (assuming std::optional is available)
// This function was previously in occupancy_map.cpp
void printOptionalTimeInterval(const std::optional<TimeInterval> &interval_opt, const std::string &context)
{
    std::cout << context;
    if (interval_opt)
    {
        const auto &interval = *interval_opt;
        std::cout << "Start: " << interval.start << ", End: " << interval.end << ", Agent: " << interval.agent_id << std::endl;
    }
    else
    {
        std::cout << "None" << std::endl;
    }
}

int main()
{
    std::cout << "--- OccupancyMap Test Suite (Standalone C++) ---" << std::endl;

    // 1. Initialization Test
    std::cout << "\n1. Initialization Test" << std::endl;
    std::array<int, 3> map_size = {100, 100, 10}; // height, width, depth
    int time_buffer = 1;
    int safety_offsets = 1;
    OccupancyMap map(map_size, time_buffer, safety_offsets);
    std::cout << "Map initialized with H=" << OccupancyMap::get_map_height()
              << ", W=" << OccupancyMap::get_map_width()
              << ", D=" << OccupancyMap::get_map_depth()
              << ", TimeBuffer=" << OccupancyMap::get_time_buffer()
              << std::endl;

    // 2. Add Path Test
    std::cout << "\n2. Add Path Test" << std::endl;
    std::vector<GridNodeData> path1_nodes;                         // Changed to GridNodeData
    path1_nodes.push_back(GridNodeData(10.0f, 10.0f, 1.0f, 0.0f)); // y, x, z, t
    path1_nodes.push_back(GridNodeData(10.0f, 11.0f, 1.0f, 1.0f));
    path1_nodes.push_back(GridNodeData(10.0f, 12.0f, 1.0f, 2.0f));
    std::string agent1_id = "agent1";
    map.add_path(path1_nodes, agent1_id);
    std::cout << "Added path for " << agent1_id << std::endl;

    // 3. Check Collision Test
    std::cout << "\n3. Check Collision Test" << std::endl;
    std::tuple<int, int, int> pos_occupied_by_agent1 = {10, 10, 1};
    int time_at_pos_agent1 = 0;

    auto collision_result1 = map.check_collision(pos_occupied_by_agent1, time_at_pos_agent1);
    std::cout << "Collision at (10,10,1) t=0: " << (std::get<0>(collision_result1) ? "Yes" : "No");
    if (std::get<0>(collision_result1) && std::get<1>(collision_result1))
    {
        std::cout << ", Agent: " << *std::get<1>(collision_result1) << std::endl;
    }
    else
    {
        std::cout << std::endl;
    }

    std::tuple<int, int, int> pos_free = {50, 50, 5};
    int time_free = 10;
    auto collision_result2 = map.check_collision(pos_free, time_free);
    std::cout << "Collision at (50,50,5) t=10: " << (std::get<0>(collision_result2) ? "Yes" : "No") << std::endl;

    // 4. Get Occupying Agents Test
    std::cout << "\n4. Get Occupying Agents Test" << std::endl;
    auto agents = map.get_occupying_agents(pos_occupied_by_agent1, time_at_pos_agent1);
    std::cout << "Agents at (10,10,1) t=0: ";
    for (const auto &agent_id_str : agents)
    {
        std::cout << agent_id_str << " ";
    }
    std::cout << std::endl;

    // 5. Add Second Path and Get Conflict Test
    std::cout << "\n5. Add Second Path and Get Conflict Test" << std::endl;
    std::vector<GridNodeData> path2_nodes; // Changed to GridNodeData
    path2_nodes.push_back(GridNodeData(10.0f, 11.0f, 1.0f, 1.0f));
    path2_nodes.push_back(GridNodeData(11.0f, 11.0f, 1.0f, 2.0f));
    std::string agent2_id = "agent2";
    map.add_path(path2_nodes, agent2_id);
    std::cout << "Added path for " << agent2_id << std::endl;

    std::tuple<int, int, int> conflict_pos = {10, 11, 1};
    int conflict_time = 1;

    auto conflict_opt = map.get_conflict(conflict_pos, conflict_pos, conflict_time);
    if (conflict_opt)
    {
        auto &conflict_data = *conflict_opt;
        std::cout << "Conflict detected at (" << std::get<0>(conflict_data) << ","
                  << std::get<1>(conflict_data) << "," << std::get<2>(conflict_data)
                  << ") t=" << std::get<3>(conflict_data) << " between "
                  << std::get<4>(conflict_data) << " and " << std::get<5>(conflict_data) << std::endl;
    }
    else
    {
        std::cout << "No conflict detected at specified point, or agents are same." << std::endl;
    }

    // 6. Find Valid Time Test
    std::cout << "\n6. Find Valid Time Test" << std::endl;
    std::vector<GridNodeData> path3_nodes_for_find_time; // Changed to GridNodeData
    path3_nodes_for_find_time.push_back(GridNodeData(10.0f, 10.0f, 1.0f, 0.0f));
    path3_nodes_for_find_time.push_back(GridNodeData(10.0f, 9.0f, 1.0f, 1.0f));
    std::string agent3_id = "agent3";
    int desired_start_time_path3 = 0;

    auto valid_time_result = map.find_valid_time(path3_nodes_for_find_time, agent3_id, desired_start_time_path3, 0, 10);
    if (std::get<0>(valid_time_result))
    {
        std::cout << "Found valid start time for " << agent3_id << ": " << *std::get<0>(valid_time_result)
                  << (std::get<1>(valid_time_result) ? " (modified from original)" : " (original time OK)") << std::endl;
    }
    else
    {
        std::cout << "Could not find valid start time for " << agent3_id << std::endl;
    }

    // 7. Remove Agent Test
    std::cout << "\n7. Remove Agent Test" << std::endl;
    map.remove_agent(agent1_id);
    std::cout << "Removed " << agent1_id << std::endl;
    auto collision_after_remove = map.check_collision(pos_occupied_by_agent1, time_at_pos_agent1);
    std::cout << "Collision at (10,10,1) t=0 after removing agent1: "
              << (std::get<0>(collision_after_remove) ? "Yes" : "No");
    if (std::get<0>(collision_after_remove) && std::get<1>(collision_after_remove))
    {
        std::cout << ", Agent: " << *std::get<1>(collision_after_remove) << std::endl;
    }
    else
    {
        std::cout << std::endl;
    }

    // 8. Clear Test
    std::cout << "\n8. Clear Test" << std::endl;
    map.clear();
    std::cout << "Map cleared." << std::endl;
    auto collision_after_clear = map.check_collision({10, 11, 1}, 1);
    std::cout << "Collision at (10,11,1) t=1 after clear: "
              << (std::get<0>(collision_after_clear) ? "Yes" : "No") << std::endl;

    // 9. Copy Test
    std::cout << "\n9. Copy Test" << std::endl;
    map.add_path(path1_nodes, agent1_id);
    OccupancyMap map_copy = map.copy();
    std::cout << "Map copied." << std::endl;
    auto collision_on_original = map.check_collision(pos_occupied_by_agent1, time_at_pos_agent1);
    auto collision_on_copy = map_copy.check_collision(pos_occupied_by_agent1, time_at_pos_agent1);
    std::cout << "Collision on original at (10,10,1) t=0: " << (std::get<0>(collision_on_original) ? "Yes" : "No") << std::endl;
    std::cout << "Collision on copy at (10,10,1) t=0: " << (std::get<0>(collision_on_copy) ? "Yes" : "No") << std::endl;

    map.clear();
    auto collision_on_copy_after_orig_clear = map_copy.check_collision(pos_occupied_by_agent1, time_at_pos_agent1);
    std::cout << "Collision on copy at (10,10,1) t=0 after original map.clear(): "
              << (std::get<0>(collision_on_copy_after_orig_clear) ? "Yes" : "No") << std::endl;

    std::cout << "\n--- Test Suite Finished ---" << std::endl;
    return 0;
}
