#ifndef OBSTACLE_TYPE_MANAGER_H
#define OBSTACLE_TYPE_MANAGER_H

#include <string>
#include <vector>
#include <set>
#include <map>
#include <tuple>     // For GridPoint3D
#include <stdexcept> // For exceptions

// Forward declaration for pybind11::array_t if we decide to use it directly in C++
// For now, we'll assume conversion happens at the binding layer.
// namespace pybind11 { template <typename T> class array_t; }

namespace CVToolbox
{

    // GridPoint3D represents (y, x, z) integer coordinates
    using GridPoint3D = std::tuple<int, int, int>;

    struct ObstacleTypeInfo
    {
        std::string description;
        // std::string created_at; // Python version uses datetime, C++ could use std::chrono or string
        // For simplicity, omitting timestamp for now unless explicitly needed by logic.
    };

    class ObstacleTypeManagerCpp
    {
    public:
        ObstacleTypeManagerCpp(int map_height, int map_width, int map_depth);

        /**
         * @brief Registers a new obstacle type.
         * @param type_name Name of the type.
         * @param description Optional description.
         * @return True if registration was successful, false if type already exists.
         */
        bool register_type(const std::string &type_name, const std::string &description = "");

        /**
         * @brief Unregisters an obstacle type and removes all its associated positions.
         * @param type_name Name of the type to unregister.
         * @param out_removed_positions Optional pointer to a set to store the positions that were removed.
         * @return True if the type was found and unregistered, false otherwise.
         */
        bool unregister_type(const std::string &type_name, std::set<GridPoint3D> *out_removed_positions = nullptr);

        /**
         * @brief Adds a single obstacle position to a given type.
         * @param type_name The type of the obstacle.
         * @param position The (y,x,z) grid coordinates of the obstacle.
         * @return True if successful, false if type does not exist.
         * @throws std::out_of_range if position is out of map bounds (optional check).
         */
        bool add_position(const std::string &type_name, const GridPoint3D &position);

        /**
         * @brief Adds a batch of obstacle positions to a given type.
         * @param type_name The type of the obstacle.
         * @param positions A vector of (y,x,z) grid coordinates.
         * @return True if successful, false if type does not exist.
         */
        bool add_positions_batch(const std::string &type_name, const std::vector<GridPoint3D> &positions);

        // Python version has a batch add from numpy bool array.
        // This would typically be handled in Pybind11 bindings by converting numpy array to std::vector<GridPoint3D>.
        // If direct C++ handling of a dense bool array is needed:
        // bool add_positions_batch_dense(const std::string& type_name, const std::vector<bool>& dense_bool_array_flat,
        //                                int height, int width, int depth);

        /**
         * @brief Removes a single obstacle position from a given type.
         * @param type_name The type of the obstacle.
         * @param position The (y,x,z) grid coordinates to remove.
         * @return True if position was found and removed, false otherwise or if type doesn't exist.
         */
        bool remove_position(const std::string &type_name, const GridPoint3D &position);

        /**
         * @brief Clears all obstacle positions for a given type.
         * @param type_name The type of the obstacle.
         * @return A set of positions that were cleared. Empty if type not found or had no positions.
         */
        std::set<GridPoint3D> clear_all_positions(const std::string &type_name);

        /**
         * @brief Gets all obstacle types present at a specific grid position.
         * @param position The (y,x,z) grid coordinates.
         * @return A vector of type names.
         */
        std::vector<std::string> get_type_at_position(const GridPoint3D &position) const;

        /**
         * @brief Gets information for all registered obstacle types.
         * @return A map of type name to ObstacleTypeInfo.
         */
        std::map<std::string, ObstacleTypeInfo> get_all_types() const;

        /**
         * @brief Gets all positions for a specific obstacle type.
         * @param type_name The type of the obstacle.
         * @return A set of (y,x,z) grid coordinates. Empty if type not found.
         */
        std::set<GridPoint3D> get_positions_set(const std::string &type_name) const;

        // The Python version can return a bool numpy array.
        // In C++, returning a dense 3D bool vector is very memory inefficient for sparse obstacles.
        // It's better to return the set of points (get_positions_set) and let the Python
        // binding layer convert this to a NumPy array if needed by the Python caller.

        /**
         * @brief Checks if a given obstacle type name is registered.
         * @param type_name The type name to check.
         * @return True if the type is registered, false otherwise.
         */
        bool is_type_registered(const std::string &type_name) const;

        /**
         * @brief Checks if a given grid point is within the defined map boundaries.
         * @param position The (y,x,z) grid coordinates.
         * @return True if the point is in bounds, false otherwise.
         */
        bool is_in_bounds(const GridPoint3D &position) const;

    private:
        int map_height_;
        int map_width_;
        int map_depth_;

        std::map<std::string, ObstacleTypeInfo> types_;
        std::map<std::string, std::set<GridPoint3D>> positions_by_type_;
    };

} // namespace CVToolbox
#endif // OBSTACLE_TYPE_MANAGER_H
