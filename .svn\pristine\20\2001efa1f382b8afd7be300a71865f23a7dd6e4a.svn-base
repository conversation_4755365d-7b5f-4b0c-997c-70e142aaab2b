from typing import Dict, List, Optional, Tuple
from datetime import datetime
from ...utils.logging import get_logger
from .base import MessageHandler
from .planning_handler import PlanningMessageHandler
from .route_handler import RouteMessageHandler

logger = get_logger(__name__)


class ApprovalMessageHandler(MessageHandler):
    """处理审批消息"""

    def __init__(
        self,
        map_3d,
        occupancy_map,
        producer=None,
        response_topic=None,
        response_topic_sora=None,
        planning_handler=None,
        route_handler=None,
    ):
        """
        初始化审批消息处理器

        Args:
            map_3d: 3D地图实例
            occupancy_map: 占用图实例
            producer: Kaf<PERSON>生产者实例
            response_topic: 响应前端主题
            response_topic_sora: SORA响应主题
            planning_handler: 可选的路径规划处理器实例
            route_handler: 可选的固定航线处理器实例
        """
        super().__init__(
            map_3d, occupancy_map, producer, response_topic, response_topic_sora
        )

        # 如果没有提供处理器，则创建新的实例
        self.planning_handler = planning_handler or PlanningMessageHandler(
            map_3d, occupancy_map, producer, response_topic, response_topic_sora
        )
        self.route_handler = route_handler or RouteMessageHandler(
            map_3d, occupancy_map, producer, response_topic, response_topic_sora
        )

    def handle_message(
        self, message: Dict, needsend=True
    ) -> Tuple[Optional[List], Optional[str]]:
        """
        处理审批消息，区分固定航线和自定义航线进行处理

        Args:
            message: 消息字典，包含审批信息

        Returns:
            Tuple[Optional[List], Optional[str]]: (路径点列表, 错误信息)
        """
        try:
            message_data = message.get("data", {})
            # is_approved = message.get("flow_risk_state", False)
            # if is_approved:  # 审核通过
            #     # 确保状态正确
            #     message["state"] = True
            #     if not message.get("reason"):
            #         message["reason"] = "审核通过"

            #     if needsend:
            #         # 发送响应，保持原始消息基本不变
            #         self._send_response(
            #             request=message,
            #             new_data={},  # 不添加新字段
            #             response_topic=self.response_topic,  # 审核通过使用审核系统响应主题
            #         )
            #     return message.get("planned_path_points"), None

            # else:  # 审核未通过
            # 从占用图中删除原有路径
            self.occupancy_map.remove_agent(message_data["flightapplyid"])
            self.planned_paths.pop(message_data["flightapplyid"], None)

            # 检查是否提供了新的起飞时间
            if "takeoff_time" not in message_data or not message_data["takeoff_time"]:
                error_msg = "审核未通过且未提供新的起飞时间"
                logger.error(error_msg)
                if needsend:
                    self._send_response(
                        request=message_data,
                        new_data={"risk_state": False, "risk_reason": error_msg},
                        response_topic=self.response_topic,
                    )
                new_data = {"risk_state": False, "risk_reason": error_msg}
                return (message_data, new_data), error_msg

            # 更新起飞时间
            # if "takeoff_time" not in message_data or not message_data["takeoff_time"]:
            #     raise ValueError("审核未通过且未提供有效的起飞时间")

            message_data["beginTime"] = message_data["takeoff_time"]
            logger.info(
                f"审核未通过，使用新的起飞时间 {message_data['takeoff_time']} 重新规划路径"
            )

            # 根据航线类型委托给对应的处理器
            if message_data.get("task_source") == '1':  # 巡检的固定航线
                return self.route_handler.handle_message(message, needsend)
            else:  # 自定义航线
                return self.planning_handler.handle_message(message, needsend)

        except Exception as e:
            error_msg = f"处理审批消息时出错: {str(e)}"
            logger.error(error_msg)
            new_data = {"risk_state": False, "risk_reason": error_msg}
            return (message_data, new_data), error_msg
