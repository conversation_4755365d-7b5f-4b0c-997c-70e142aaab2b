#pragma once

#include <array>
#include <vector>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <optional>
// #include <memory> // No longer needed for std::shared_ptr<GridNode>
#include "time_interval.h"
#include "grid_node_data.h" // Changed from grid_node.h

// Hash function for std::tuple<int, int, int>
namespace std
{
    template <>
    struct hash<tuple<int, int, int>>
    {
        size_t operator()(const tuple<int, int, int> &t) const
        {
            auto [a, b, c] = t;
            size_t h1 = std::hash<int>{}(a);
            size_t h2 = std::hash<int>{}(b);
            size_t h3 = std::hash<int>{}(c);
            return h1 ^ (h2 << 1) ^ (h3 << 2);
        }
    };
}

class OccupancyMap
{
private:
    static std::array<int, 3> map_dimensions_;
    static int map_height_;
    static int map_width_;
    static int map_depth_;
    static int time_buffer_;

    static std::unordered_map<std::tuple<int, int, int>, std::vector<TimeInterval>> occupancy_data_;
    static std::unordered_map<std::string, std::unordered_set<std::tuple<int, int, int>>> agent_spatial_occupancy_;

    int safety_offsets_val_;

    std::vector<TimeInterval> _merge_intervals(std::vector<TimeInterval> &intervals) const;

public:
    OccupancyMap(const std::array<int, 3> &map_size_param, int time_buffer_param, int safety_offsets_param);

    /**
     * @brief Adds a path to the occupancy map.
     * @param path List of path nodes (GridNodeData with y, x, z, t).
     * @param agent_id The ID of the agent.
     */
    void add_path(const std::vector<GridNodeData> &path, const std::string &agent_id);

    std::tuple<bool, std::optional<std::string>> check_collision(const std::tuple<int, int, int> &pos, int t) const;

    std::optional<std::tuple<int, int, int, int, std::string, std::string>>
    get_conflict(const std::tuple<int, int, int> &pos1, const std::tuple<int, int, int> &pos2, int t) const;

    std::unordered_set<std::string> get_occupying_agents(const std::tuple<int, int, int> &pos, int t) const;

    void clear();

    void remove_agent(const std::string &agent_id);

    /**
     * @brief Finds a valid (conflict-free) start time for a given path.
     * @param path The path to check (list of GridNodeData).
     * @param agent_id The agent ID.
     * @param start_time The desired start time.
     * @param time_direction Search direction (1: forward, -1: backward, 0: bidirectional).
     * @param max_offset Maximum time offset allowed for searching.
     * @return Tuple {std::optional<int> valid_time, bool time_was_modified}.
     */
    std::tuple<std::optional<int>, bool> find_valid_time(
        const std::vector<GridNodeData> &path, // Changed from std::vector<std::shared_ptr<GridNode>>
        const std::string &agent_id,
        int start_time,
        int time_direction = 0,
        int max_offset = 3600) const;

    OccupancyMap copy() const;

    static std::array<int, 3> get_map_dimensions() { return map_dimensions_; }
    static int get_map_height() { return map_height_; }
    static int get_map_width() { return map_width_; }
    static int get_map_depth() { return map_depth_; }
    static int get_time_buffer() { return time_buffer_; }
};
