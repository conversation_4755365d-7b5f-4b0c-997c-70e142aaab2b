#include "grid_converter.h" // Should be "../include/grid_converter.h" if src and include are peers
#include <stdexcept>        // For std::invalid_argument

namespace CVToolbox
{

    GridConverterCpp::GridConverterCpp(const MapGridConfig &config) : config_(config)
    {
        if (config_.cell_size_lat_deg == 0.0)
        {
            throw std::invalid_argument("cell_size_lat_deg cannot be zero.");
        }
        if (config_.cell_size_lon_deg == 0.0)
        {
            throw std::invalid_argument("cell_size_lon_deg cannot be zero.");
        }
        if (config_.cell_size_alt_m == 0.0)
        {
            throw std::invalid_argument("cell_size_alt_m cannot be zero.");
        }
        // Pre-calculations can be done here if needed, e.g.,
        // meters_per_degree_lat_ = 111319.492; // Approximate, can be refined
        // meters_per_degree_lon_at_ref_lat_ = meters_per_degree_lat_ * std::cos(to_radians(config_.ref_lat));
        // However, for direct degree-to-grid conversion, these are not strictly necessary
        // as we are dividing by degree cell sizes.
    }

    std::tuple<double, double, double> GridConverterCpp::geo_to_relative_double(
        double lat, double lon, double alt_msl) const
    {

        double grid_y = (lat - config_.ref_lat) / config_.cell_size_lat_deg;
        double grid_x = (lon - config_.ref_lon) / config_.cell_size_lon_deg;
        double grid_z = (alt_msl - config_.ref_alt_msl) / config_.cell_size_alt_m;

        return std::make_tuple(grid_y, grid_x, grid_z);
    }

    std::tuple<int, int, int> GridConverterCpp::geo_to_relative_int(
        double lat, double lon, double alt_msl) const
    {

        auto [grid_y_double, grid_x_double, grid_z_double] = geo_to_relative_double(lat, lon, alt_msl);

        // Round to nearest integer for grid indices
        int grid_y = static_cast<int>(std::round(grid_y_double));
        int grid_x = static_cast<int>(std::round(grid_x_double));
        int grid_z = static_cast<int>(std::round(grid_z_double));

        return std::make_tuple(grid_y, grid_x, grid_z);
    }

    std::tuple<double, double, double> GridConverterCpp::relative_to_geo_double(
        double grid_y, double grid_x, double grid_z) const
    {

        double lat = config_.ref_lat + (grid_y * config_.cell_size_lat_deg);
        double lon = config_.ref_lon + (grid_x * config_.cell_size_lon_deg);
        double alt_msl = config_.ref_alt_msl + (grid_z * config_.cell_size_alt_m);

        return std::make_tuple(lat, lon, alt_msl);
    }

    std::tuple<double, double, double> GridConverterCpp::relative_to_geo_int(
        int grid_y, int grid_x, int grid_z) const
    {
        // For integer grid inputs, assume they refer to the center of the grid cell.
        // So, add 0.5 to grid coordinates before converting back.
        // This matches how some systems interpret discrete grid cell to continuous coordinate mapping.
        // If the integer grid (y,x,z) refers to the corner, then 0.5 should not be added.
        // Python version's behavior should be checked for consistency.
        // Assuming Python's GridConverter.relative_to_geo takes integer grid indices
        // and returns the geo coordinate of the grid cell's center.
        return relative_to_geo_double(
            static_cast<double>(grid_y) + 0.5,
            static_cast<double>(grid_x) + 0.5,
            static_cast<double>(grid_z) + 0.5);
    }

} // namespace CVToolbox
