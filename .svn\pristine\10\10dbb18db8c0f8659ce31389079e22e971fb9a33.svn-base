<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="79f2e9aa-120c-468d-bc21-4f694459f222" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/config.json" beforeDir="false" afterPath="$PROJECT_DIR$/config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/config/settings.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/config/settings.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/map/map_handler_3d.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/map/map_handler_3d.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/handlers/message_handlers/base.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/handlers/message_handlers/base.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/handlers/message_handlers/planning_handler.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/handlers/message_handlers/planning_handler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/handlers/risk_assessment_consumer.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/handlers/risk_assessment_consumer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/tests/test_producer.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/tests/test_producer.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/miniconda3/envs/py3913/Lib/site-packages/mysql/connector/pooling.py" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/core/pathfinding/jps_v4.py" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2v46OLemmGZr3yaUZSd0jyJNQql" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.main.executor": "Run",
    "Python.test_producer.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "feature/codebase-refactor",
    "ignore.virus.scanning.warn.message": "true",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "D:/crscu/route_palnning/codes/Multi-agent-pathfinding-3d-LC-redis-v4-20250430",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.lookFeel",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Python.main">
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Multi-agent-pathfinding-3d-LC-redis-v4" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_producer" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Multi-agent-pathfinding-3d-LC-redis-v4" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src/tests" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/tests/test_producer.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.test_producer" />
        <item itemvalue="Python.main" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.122" />
        <option value="bundled-python-sdk-880ecab49056-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.122" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="79f2e9aa-120c-468d-bc21-4f694459f222" name="更改" comment="" />
      <created>1743391972499</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743391972499</updated>
      <workItem from="1743391973532" duration="2305000" />
      <workItem from="1743397023704" duration="12564000" />
      <workItem from="1743469367301" duration="23269000" />
      <workItem from="1743556087342" duration="4081000" />
      <workItem from="1743564562677" duration="12683000" />
      <workItem from="1743584976999" duration="6676000" />
      <workItem from="1744028385853" duration="3154000" />
      <workItem from="1744034110226" duration="783000" />
      <workItem from="1744075597616" duration="2941000" />
      <workItem from="1744163731200" duration="12000" />
      <workItem from="1745742522932" duration="368000" />
      <workItem from="1745742991699" duration="498000" />
      <workItem from="1745743501915" duration="45000" />
      <workItem from="1745743564205" duration="3850000" />
      <workItem from="1745892982361" duration="6578000" />
      <workItem from="1745983808725" duration="110000" />
      <workItem from="1745983948335" duration="8962000" />
      <workItem from="1746327920916" duration="10328000" />
      <workItem from="1746494252107" duration="40971000" />
      <workItem from="1746666936238" duration="126000" />
      <workItem from="1746667074767" duration="11768000" />
      <workItem from="1746753392868" duration="14344000" />
      <workItem from="1747012051626" duration="894000" />
      <workItem from="1747013001974" duration="3537000" />
      <workItem from="1747017713982" duration="89000" />
      <workItem from="1747018240723" duration="71000" />
      <workItem from="1747031557485" duration="322000" />
      <workItem from="1747098762185" duration="45000" />
      <workItem from="1747099504348" duration="7591000" />
      <workItem from="1747113190408" duration="14214000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="python:kafka-python" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/Multi_agent_pathfinding_3d_LC_redis_v4_20250430$test_producer.coverage" NAME="test_producer 覆盖结果" MODIFIED="1747185842465" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src/tests" />
    <SUITE FILE_PATH="coverage/Multi_agent_pathfinding_3d_LC_redis_v4_20250428$main.coverage" NAME="main 覆盖结果" MODIFIED="1745917115614" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Multi_agent_pathfinding_3d_LC_redis_v4_20250428$test_producer.coverage" NAME="test_producer 覆盖结果" MODIFIED="1745895285299" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src/tests" />
    <SUITE FILE_PATH="coverage/Multi_agent_pathfinding_3d_LC_redis_v4$main.coverage" NAME="main 覆盖结果" MODIFIED="1745803339474" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Multi_agent_pathfinding_3d_LC_redis_v4$test_producer.coverage" NAME="test_producer 覆盖结果" MODIFIED="1745803346547" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src/tests" />
    <SUITE FILE_PATH="coverage/Multi_agent_pathfinding_3d_LC_redis_v4_20250430$main.coverage" NAME="main 覆盖结果" MODIFIED="1747185788604" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>