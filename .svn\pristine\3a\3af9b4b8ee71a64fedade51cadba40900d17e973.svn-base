0000000000000000000000000000000000000000 5f3daac60e0d7ea7a4e4dfd2c2bc9f0bc6174a33 liuchang1230 <<EMAIL>> 1736739877 +0800	commit (initial): Initial commit with project files
5f3daac60e0d7ea7a4e4dfd2c2bc9f0bc6174a33 0000000000000000000000000000000000000000 liuchang1230 <<EMAIL>> 1736739883 +0800	Branch: renamed refs/heads/master to refs/heads/main
0000000000000000000000000000000000000000 5f3daac60e0d7ea7a4e4dfd2c2bc9f0bc6174a33 liuchang1230 <<EMAIL>> 1736739883 +0800	Branch: renamed refs/heads/master to refs/heads/main
5f3daac60e0d7ea7a4e4dfd2c2bc9f0bc6174a33 1e3e55074ba92afc41ea563f75e59851d8abaa94 liuchang1230 <<EMAIL>> 1736820371 +0800	commit: Update grid converter, kafka consumer and low level policy: Improve time parsing and path planning logic
1e3e55074ba92afc41ea563f75e59851d8abaa94 8885ee5fbc52f637450e3002e2746f2275f4a546 liuchang1230 <<EMAIL>> 1737016224 +0800	commit: Update multiple files:
8885ee5fbc52f637450e3002e2746f2275f4a546 1e3e55074ba92afc41ea563f75e59851d8abaa94 liuchang1230 <<EMAIL>> 1737422857 +0800	reset: moving to HEAD~1
1e3e55074ba92afc41ea563f75e59851d8abaa94 5f3daac60e0d7ea7a4e4dfd2c2bc9f0bc6174a33 liuchang1230 <<EMAIL>> 1737422900 +0800	reset: moving to HEAD~1
5f3daac60e0d7ea7a4e4dfd2c2bc9f0bc6174a33 8885ee5fbc52f637450e3002e2746f2275f4a546 liuchang1230 <<EMAIL>> 1737422950 +0800	reset: moving to 8885ee5
8885ee5fbc52f637450e3002e2746f2275f4a546 8885ee5fbc52f637450e3002e2746f2275f4a546 liuchang1230 <<EMAIL>> 1737423069 +0800	reset: moving to origin/main
8885ee5fbc52f637450e3002e2746f2275f4a546 8885ee5fbc52f637450e3002e2746f2275f4a546 liuchang1230 <<EMAIL>> 1737446275 +0800	checkout: moving from main to time-safety-margin
8885ee5fbc52f637450e3002e2746f2275f4a546 75ef07188a3c3560a9138b24b1dc6207978bd136 liuchang1230 <<EMAIL>> 1737446303 +0800	commit: Add time safety margin and update occupancy map logic: - Add time safety margin for better path planning - Update occupancy map logic for improved collision avoidance - Modify related components to support these changes
75ef07188a3c3560a9138b24b1dc6207978bd136 3b178d7d660f3394897a136be7392a07b5497385 liuchang1230 <<EMAIL>> 1737537141 +0800	commit: Add route validator and update core components: - Add route_validator.py for path validation - Update path planning logic in multiple files - Improve occupancy map handling
3b178d7d660f3394897a136be7392a07b5497385 e7837081617dead61af871c25651fc3eaad6d31a liuchang1230 <<EMAIL>> 1737684081 +0800	commit: Update path planning components: - Improve node handling and occupancy map logic - Remove deprecated files (mydebug.py, occupancy_map_3d.py) - Enhance route validation and testing - Update Kafka consumer for better request handling
e7837081617dead61af871c25651fc3eaad6d31a d129566186a99b5899fd25fdda37ff821e3ca7cd liuchang1230 <<EMAIL>> 1737857005 +0800	commit: Update optimized_path_finder.py: optimize path finding algorithm
d129566186a99b5899fd25fdda37ff821e3ca7cd d129566186a99b5899fd25fdda37ff821e3ca7cd liuchang1230 <<EMAIL>> 1738587021 +0800	checkout: moving from time-safety-margin to jump-point-search
d129566186a99b5899fd25fdda37ff821e3ca7cd 19a5b8ec7dd68bd93bd18c9d4d0f831da8a92b7e liuchang1230 <<EMAIL>> 1738587064 +0800	commit: feat: Add Jump Point Search implementation for 3D path planning
19a5b8ec7dd68bd93bd18c9d4d0f831da8a92b7e 7c6028ccad76c1f0a264552fcf854940dd9a5d1e liuchang1230 <<EMAIL>> 1738665875 +0800	commit: feat: Complete JPS 3D implementation with performance benchmarks\n- Add optimized neighbor pruning in 3D space\n- Update path validation logic\n- Add performance test cases
7c6028ccad76c1f0a264552fcf854940dd9a5d1e 3697c621af8abf9ab7041d03407fc6c7833e2f7e liuchang1230 <<EMAIL>> 1738721460 +0800	commit: feat: Complete JPS 3D implementation with performance benchmarks\n- Add optimized neighbor pruning in 3D space\n- Update path validation logic\n- Add performance test cases
3697c621af8abf9ab7041d03407fc6c7833e2f7e f3e491ebaf5f4d6f4300ca91ce580ce54a939ae8 liuchang1230 <<EMAIL>> 1738755327 +0800	commit: feat: Complete JPS 3D implementation with performance benchmarks\n- Add optimized neighbor pruning in 3D space\n- Update path validation logic\n- Add performance test cases
f3e491ebaf5f4d6f4300ca91ce580ce54a939ae8 e817b55a9cf40d25ce97a12eb795499982f430ad liuchang1230 <<EMAIL>> 1739175746 +0800	commit: 增加了对角移动的支持
e817b55a9cf40d25ce97a12eb795499982f430ad 78bd0dc68ed07cd5abef1f33a2e2a9d262754b11 liuchang1230 <<EMAIL>> ********** +0800	commit (amend): 增加了对角移动的支持
78bd0dc68ed07cd5abef1f33a2e2a9d262754b11 78bd0dc68ed07cd5abef1f33a2e2a9d262754b11 liuchang1230 <<EMAIL>> ********** +0800	checkout: moving from jump-point-search to feature/no-fly-zone-auto-reroute
78bd0dc68ed07cd5abef1f33a2e2a9d262754b11 576671a8073a7fee699e59c2de21979ea0f415a5 liuchang1230 <<EMAIL>> ********** +0800	commit: feat: add auto-rerouting for no-fly zones
576671a8073a7fee699e59c2de21979ea0f415a5 e29fb8b743cf9dee34aaa7b61338e144974564cb liuchang1230 <<EMAIL>> ********** +0800	commit: update: modify code for handling no-fly zones
e29fb8b743cf9dee34aaa7b61338e144974564cb e29fb8b743cf9dee34aaa7b61338e144974564cb liuchang1230 <<EMAIL>> ********** +0800	reset: moving to HEAD
e29fb8b743cf9dee34aaa7b61338e144974564cb 8885ee5fbc52f637450e3002e2746f2275f4a546 liuchang1230 <<EMAIL>> ********** +0800	checkout: moving from feature/no-fly-zone-auto-reroute to main
8885ee5fbc52f637450e3002e2746f2275f4a546 8885ee5fbc52f637450e3002e2746f2275f4a546 liuchang1230 <<EMAIL>> ********** +0800	checkout: moving from main to feature/codebase-refactor
8885ee5fbc52f637450e3002e2746f2275f4a546 0de45b6e00c337088f003f55174c663d28d86418 liuchang1230 <<EMAIL>> ********** +0800	commit: refactor: restructure project with modular architecture
0de45b6e00c337088f003f55174c663d28d86418 56374557f245e2442d677ea2e0eae2e3afdca4c4 liuchang1230 <<EMAIL>> 1745892535 +0800	commit: refactor: 重构代码结构，优化路径规划算法
56374557f245e2442d677ea2e0eae2e3afdca4c4 7a1eb5bd9a290ea6b8de579a39ec69bc7c78da30 liuchang1230 <<EMAIL>> 1746779954 +0800	commit: 修改 Settings 类，使其能够从多个位置查找配置文件，提高配置文件加载的灵活性。添加命令行参数支持，允许通过命令行指定关键配置参数：--config: 指定配置文件路径, --location: 指定地点（如 nanjing, shijiazhuang）, --server: 指定服务器地址。命令行参数的优先级高于配置文件中的设置，提供更灵活的配置方式。
