import numpy as np
from typing import Set, Tuple, List, Optional
import time


def extract_polygon_boundary(
    polygon: np.ndarray, boundary_thickness: int = 1
) -> np.ndarray:
    """
    提取多边形的边界点，生成一个具有指定厚度的边界

    Args:
        polygon: 多边形顶点坐标，形状为(n, 2)
        boundary_thickness: 边界厚度（网格单位）

    Returns:
        np.ndarray: 边界点坐标，形状为(m, 2)
    """
    # 确保多边形是闭合的（首尾相连）
    if not np.array_equal(polygon[0], polygon[-1]):
        polygon = np.vstack([polygon, polygon[0]])

    # 计算多边形的边界框
    y_min, x_min = np.floor(np.min(polygon, axis=0)).astype(int)
    y_max, x_max = np.ceil(np.max(polygon, axis=0)).astype(int)

    # 使用NumPy向量化操作处理边界生成
    # 存储所有边界点的列表
    all_boundary_points = []
    for i in range(len(polygon) - 1):
        p1 = polygon[i]
        p2 = polygon[i + 1]

        # 计算边的长度
        length = np.linalg.norm(p2 - p1)

        # 根据边的长度确定采样点数量（至少2个点）
        num_samples = max(2, int(length * 2))

        # 在边上均匀采样 - 使用NumPy向量化操作
        t_values = np.linspace(0, 1, num_samples)

        # 计算所有采样点的坐标
        points = p1[np.newaxis, :] + t_values[:, np.newaxis] * (p2 - p1)[np.newaxis, :]
        y_values = np.round(points[:, 0]).astype(int)
        x_values = np.round(points[:, 1]).astype(int)

        # 为每个采样点创建偏移网格
        for y_int, x_int in zip(y_values, x_values):
            # 创建偏移坐标的网格
            oy_values, ox_values = np.meshgrid(
                np.arange(-boundary_thickness, boundary_thickness + 1),
                np.arange(-boundary_thickness, boundary_thickness + 1),
                indexing="ij",
            )

            # 计算曼哈顿距离
            manhattan_dist = np.abs(ox_values) + np.abs(oy_values)

            # 找出在边界厚度内的点
            mask = manhattan_dist <= boundary_thickness

            # 获取满足条件的偏移坐标
            valid_oy = oy_values[mask]
            valid_ox = ox_values[mask]

            # 计算最终坐标
            final_y = y_int + valid_oy
            final_x = x_int + valid_ox

            # 筛选在边界框内的点
            valid_mask = (
                (final_y >= y_min)
                & (final_y <= y_max)
                & (final_x >= x_min)
                & (final_x <= x_max)
            )

            # 添加到边界点列表
            valid_points = np.column_stack((final_y[valid_mask], final_x[valid_mask]))
            all_boundary_points.append(valid_points)

    # 如果有边界点，合并所有点并去除重复
    if all_boundary_points:
        # 合并所有边界点
        all_points = np.vstack(all_boundary_points)
        # 去除重复点
        unique_points = np.unique(all_points, axis=0)
        return unique_points
    else:
        # 如果没有边界点，返回空数组
        return np.zeros((0, 2), dtype=int)


def extract_cylinder_boundary(
    center: Tuple[float, float], radius: float, boundary_thickness: int = 1
) -> np.ndarray:
    """
    提取圆柱体的边界点，生成一个具有指定厚度的环形边界

    Args:
        center: 圆心坐标 (y, x)
        radius: 半径
        boundary_thickness: 边界厚度（网格单位）

    Returns:
        np.ndarray: 边界点坐标，形状为(m, 2)
    """
    # 计算圆的边界框
    y_center, x_center = center
    y_min = int(np.floor(y_center - radius - boundary_thickness))
    y_max = int(np.ceil(y_center + radius + boundary_thickness))
    x_min = int(np.floor(x_center - radius - boundary_thickness))
    x_max = int(np.ceil(x_center + radius + boundary_thickness))

    # 计算半径的平方，用于快速判断点是否在圆内
    # radius_sq = radius**2
    inner_radius_sq = (
        (radius - boundary_thickness) ** 2 if radius > boundary_thickness else 0
    )
    outer_radius_sq = (radius + boundary_thickness) ** 2

    # 使用NumPy向量化操作处理
    # 创建网格坐标
    y_coords, x_coords = np.meshgrid(
        np.arange(y_min, y_max + 1), np.arange(x_min, x_max + 1), indexing="ij"
    )

    # 计算每个点到圆心的距离平方
    dist_sq = (y_coords - y_center) ** 2 + (x_coords - x_center) ** 2

    # 创建掩码，标识边界点
    # 如果点在外圆内且不在内圆内，则它在边界上
    mask = (dist_sq <= outer_radius_sq) & (
        (dist_sq >= inner_radius_sq) | (radius <= boundary_thickness)
    )

    # 获取满足条件的点的坐标
    boundary_y = y_coords[mask]
    boundary_x = x_coords[mask]

    # 直接创建NumPy数组，并去除重复点
    # 将坐标组合成一个二维数组
    points = np.column_stack((boundary_y.astype(int), boundary_x.astype(int)))

    # 使用np.unique去除重复点
    unique_points = np.unique(points, axis=0)

    return unique_points


def generate_boundary_points_3d(
    boundary_points_2d: np.ndarray, max_height: int
) -> Set[Tuple[int, int, int]]:
    """
    根据2D边界点生成3D边界点集合

    Args:
        boundary_points_2d: 2D边界点坐标，形状为(n, 2)
        max_height: 最大高度

    Returns:
        Set[Tuple[int, int, int]]: 3D边界点集合
    """
    start_time = time.time()

    # 使用NumPy向量化操作生成3D边界点
    # 获取2D边界点数量
    n_points = len(boundary_points_2d)

    # 创建高度数组
    z_values = np.arange(max_height)

    # 为每个2D点创建所有高度的组合
    # 重复每个2D点max_height次
    y_coords = np.repeat(boundary_points_2d[:, 0], max_height)
    x_coords = np.repeat(boundary_points_2d[:, 1], max_height)

    # 对每个2D点，生成所有高度值
    z_coords = np.tile(z_values, n_points)

    # 直接创建NumPy数组，并去除重复点
    # 将坐标组合成一个三维数组
    points = np.column_stack((y_coords.astype(int), x_coords.astype(int), z_coords))

    # 使用np.unique去除重复点
    unique_points = np.unique(points, axis=0)

    # 转换为集合形式，因为函数返回类型是Set[Tuple[int, int, int]]
    boundary_points_3d = set(map(tuple, unique_points))

    elapsed = time.time() - start_time
    # print(f"生成了 {len(boundary_points_3d)} 个3D边界点，耗时 {elapsed:.2f} 秒")

    return boundary_points_3d


def generate_polygon_boundary_points_3d(
    polygon: np.ndarray,
    max_height: int,
    boundary_thickness: int = 1,
) -> Set[Tuple[int, int, int]]:
    """
    生成多边形禁飞区的3D边界点集合

    Args:
        polygon: 多边形顶点坐标，形状为(n, 2)
        max_height: 最大高度
        boundary_thickness: 水平方向的边界厚度

    Returns:
        Set[Tuple[int, int, int]]: 3D边界点集合
    """
    # 提取2D边界点
    boundary_points_2d = extract_polygon_boundary(polygon, boundary_thickness)

    # 生成3D边界点
    return generate_boundary_points_3d(boundary_points_2d, max_height)


def generate_cylinder_boundary_points_3d(
    center: Tuple[float, float],
    radius: float,
    max_height: int,
    boundary_thickness: int = 1,
) -> Set[Tuple[int, int, int]]:
    """
    生成圆柱体禁飞区的3D边界点集合

    Args:
        center: 圆心坐标 (y, x)
        radius: 半径
        max_height: 最大高度
        boundary_thickness: 水平方向的边界厚度

    Returns:
        Set[Tuple[int, int, int]]: 3D边界点集合
    """
    # 提取2D边界点
    boundary_points_2d = extract_cylinder_boundary(center, radius, boundary_thickness)

    # 生成3D边界点
    return generate_boundary_points_3d(boundary_points_2d, max_height)
