from datetime import datetime
from typing import Dict, List, Tuple, Optional, Set
import numpy as np
from ...utils.logging import get_logger

logger = get_logger(__name__)


class ObstacleTypeManager:
    """
    障碍物类型管理器
    负责管理不同类型的障碍物及其位置信息
    使用集合存储障碍物位置，以节省内存
    """

    def __init__(self, height: int, width: int, depth: int):
        """
        初始化障碍物类型管理器

        Args:
            height: 地图高度
            width: 地图宽度
            depth: 地图深度
        """
        self.height = height
        self.width = width
        self.depth = depth
        self._types: Dict[str, Dict] = {}  # 类型信息: name -> {description, created_at}
        self._positions: Dict[str, Set[Tuple[int, int, int]]] = (
            {}
        )  # 类型位置: name -> 位置集合        # 新增：存储轨道点序
        self._orbit_paths: Dict[str, List[Tuple[int, int]]] = (
            {}
        )  # 禁飞区名称 -> 有序轨道点序列
        # 新增：存储轨道点序的8个象限
        self._orbit_quadrants: Dict[str, Dict[str, Set[int]]] = (
            {}
        )  # 禁飞区名称 -> {象限名: 点索引集合}

    def register_type(self, type_name: str, description: str = None) -> None:
        """
        注册新的障碍物类型

        Args:
            type_name: 类型名称
            description: 类型描述

        Raises:
            ValueError: 当类型已存在时
        """
        if type_name in self._types:
            error_msg = f"障碍物类型 '{type_name}' 已存在"
            # logger.error(error_msg)
            raise ValueError(error_msg)
            # logger.error(error_msg)
            # return None

        self._types[type_name] = {
            "description": description,
            "created_at": datetime.now(),
        }
        # 初始化为空集合
        self._positions[type_name] = set()

    def unregister_type(self, type_name: str) -> Set[Tuple[int, int, int]]:
        """
        完全移除一个障碍物类型及其所有位置

        Args:
            type_name: 要移除的类型名称

        Returns:
            set: 该类型占用的所有位置集合

        Raises:
            ValueError: 当类型不存在时
        """
        if type_name not in self._types:
            error_msg = f"障碍物类型 '{type_name}' 不存在"
            logger.error(error_msg)
            return None

        # 获取该类型的所有位置
        positions = self._positions[type_name].copy()

        # 清理类型数据
        del self._types[type_name]
        del self._positions[type_name]

        return positions

    def add_position(self, type_name: str, position: Tuple[int, int, int]) -> None:
        """
        添加位置到指定类型

        Args:
            type_name: 障碍物类型名称
            position: 位置坐标 (y,x,z)

        Raises:
            ValueError: 当类型不存在时
        """
        if type_name not in self._types:
            error_msg = f"未知的障碍物类型: '{type_name}'"
            logger.error(error_msg)
            return None
        self._positions[type_name].add(position)

    def add_positions_batch(self, type_name: str, positions) -> None:
        """
        批量添加位置到指定类型

        Args:
            type_name: 障碍物类型名称
            positions: 可以是以下格式之一:
                      - 布尔数组，形状为(height, width, depth)，True表示该位置为障碍物
                      - 位置集合或列表，每个元素为(y,x,z)元组

        Raises:
            ValueError: 当类型不存在时
        """
        if type_name not in self._types:
            error_msg = f"未知的障碍物类型: '{type_name}'"
            logger.error(error_msg)
            return None

        # 如果是numpy数组，转换为位置集合
        if isinstance(positions, np.ndarray):
            if positions.dtype == bool:
                # 布尔数组，获取True的位置
                position_set = set(map(tuple, np.argwhere(positions)))
                self._positions[type_name].update(position_set)
            else:
                # 假设是坐标数组，每行是(y,x,z)
                self._positions[type_name].update(map(tuple, positions))
        else:
            # 假设是位置集合或列表
            self._positions[type_name].update(positions)

    def remove_position(self, type_name: str, position: Tuple[int, int, int]) -> None:
        """
        从指定类型中移除位置

        Args:
            type_name: 障碍物类型名称
            position: 位置坐标 (y,x,z)
        """
        if type_name in self._positions:
            self._positions[type_name].discard(position)

    def get_type_at_position(self, position: Tuple[int, int, int]) -> List[str]:
        """
        获取指定位置的所有障碍物类型

        Args:
            position: 位置坐标 (y,x,z)

        Returns:
            list: 该位置的所有障碍物类型名称列表
        """
        return [
            type_name
            for type_name, positions in self._positions.items()
            if position in positions
        ]

    def get_all_types(self) -> Dict[str, Dict]:
        """
        获取所有已注册的类型信息

        Returns:
            dict: 所有类型及其信息的字典
        """
        return dict(self._types)

    def get_positions(self, type_name: str, return_array: bool = False):
        """
        获取指定类型的所有位置

        Args:
            type_name: 障碍物类型名称
            return_array: True返回bool数组，False返回坐标集合

        Returns:
            如果return_array为True，返回3D布尔数组
            如果return_array为False，返回位置坐标集合
        """
        if type_name not in self._positions:
            return (
                set()
                if not return_array
                else np.zeros((self.height, self.width, self.depth), dtype=bool)
            )

        if return_array:
            # 只在需要时创建数组
            array = np.zeros((self.height, self.width, self.depth), dtype=bool)
            positions = self._positions[type_name]
            if positions:
                # 将位置集合转换为数组索引
                positions_array = np.array(list(positions))
                if len(positions_array) > 0:
                    y_coords = positions_array[:, 0]
                    x_coords = positions_array[:, 1]
                    z_coords = positions_array[:, 2]
                    array[y_coords, x_coords, z_coords] = True
            return array
        else:
            return self._positions[type_name]

    def clear_all_positions(self, type_name: str) -> Set[Tuple[int, int, int]]:
        """
        清除指定类型的所有位置

        Args:
            type_name: 障碍物类型名称

        Returns:
            set: 被清除的所有位置坐标集合
        """
        if type_name not in self._positions:
            return set()

        # 获取所有位置的副本
        positions = self._positions[type_name].copy()
        # 清空集合
        self._positions[type_name].clear()
        return positions

    def set_orbit_path(
        self, zone_name: str, orbit_points: List[Tuple[int, int]]
    ) -> None:
        """
        设置禁飞区的轨道点序

        Args:
            zone_name: 禁飞区名称
            orbit_points: 有序的轨道点序列，每个点为(y, x)坐标
        """
        self._orbit_paths[zone_name] = orbit_points

    def get_orbit_path(self, zone_name: str) -> List[Tuple[int, int]]:
        """
        获取禁飞区的轨道点序

        Args:
            zone_name: 禁飞区名称

        Returns:
            List[Tuple[int, int]]: 有序的轨道点序列，如果不存在则返回空列表"""
        return self._orbit_paths.get(zone_name, [])

    def remove_orbit_path(self, zone_name: str) -> List[Tuple[int, int]]:
        """
        移除禁飞区的轨道点序及其象限分类

        Args:
            zone_name: 禁飞区名称

        Returns:
            List[Tuple[int, int]]: 被移除的轨道点序列，如果不存在则返回空列表
        """
        # 同时移除象限信息
        self._orbit_quadrants.pop(zone_name, {})
        return self._orbit_paths.pop(zone_name, [])

    def set_orbit_quadrants(
        self, zone_name: str, quadrants: Dict[str, Set[int]]
    ) -> None:
        """
        设置禁飞区轨道点序的8个象限分类

        Args:
            zone_name: 禁飞区名称
            quadrants: 象限字典，键为象限名称，值为对应象限的点索引集合
        """
        self._orbit_quadrants[zone_name] = quadrants

    def get_orbit_quadrants(self, zone_name: str) -> Dict[str, Set[int]]:
        """
        获取禁飞区轨道点序的8个象限分类

        Args:
            zone_name: 禁飞区名称

        Returns:
            Dict[str, Set[int]]: 象限字典，如果不存在则返回空字典
        """
        return self._orbit_quadrants.get(zone_name, {})

    def remove_orbit_quadrants(self, zone_name: str) -> Dict[str, Set[int]]:
        """
        移除禁飞区轨道点序的8个象限分类

        Args:
            zone_name: 禁飞区名称

        Returns:
            Dict[str, Set[int]]: 被移除的象限字典，如果不存在则返回空字典
        """
        return self._orbit_quadrants.pop(zone_name, {})

    def get_quadrant_points(
        self, zone_name: str, quadrant_name: str
    ) -> List[Tuple[int, int]]:
        """
        获取指定象限的轨道点坐标

        Args:
            zone_name: 禁飞区名称
            quadrant_name: 象限名称

        Returns:
            List[Tuple[int, int]]: 该象限的轨道点坐标列表
        """
        orbit_points = self.get_orbit_path(zone_name)
        quadrants = self.get_orbit_quadrants(zone_name)

        if not orbit_points or not quadrants or quadrant_name not in quadrants:
            return []

        indices = quadrants[quadrant_name]
        return [orbit_points[i] for i in indices if i < len(orbit_points)]
