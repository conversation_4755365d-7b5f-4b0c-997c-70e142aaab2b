"""
并行消息处理模块
用于对风险评估消息进行分组和并行处理
"""

import numpy as np
import threading
import time
import logging
from typing import Dict, List, Tuple, Set, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from ..utils.logging import get_logger
from ..config import settings

logger = get_logger(__name__)


class ParallelMessageProcessor:
    """并行消息处理器，用于对风险评估消息进行分组和并行处理"""

    def __init__(self, max_workers=4):
        """
        初始化并行消息处理器

        Args:
            max_workers: 最大工作线程数
        """
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(
            max_workers=max_workers, thread_name_prefix="MessageProcessor"
        )

    def calculate_iou(
        self,
        rect1: Tuple[float, float, float, float],
        rect2: Tuple[float, float, float, float],
    ) -> float:
        """
        计算两个矩形的IOU (Intersection over Union)

        Args:
            rect1: 第一个矩形 (y_min, x_min, y_max, x_max)
            rect2: 第二个矩形 (y_min, x_min, y_max, x_max)

        Returns:
            float: IOU值，范围[0, 1]
        """
        # 计算交集区域
        y_min_intersect = max(rect1[0], rect2[0])
        x_min_intersect = max(rect1[1], rect2[1])
        y_max_intersect = min(rect1[2], rect2[2])
        x_max_intersect = min(rect1[3], rect2[3])

        # 如果没有交集，返回0
        if y_max_intersect < y_min_intersect or x_max_intersect < x_min_intersect:
            return 0.0

        # 计算交集面积
        intersect_area = (y_max_intersect - y_min_intersect) * (
            x_max_intersect - x_min_intersect
        )

        # 计算两个矩形的面积
        rect1_area = (rect1[2] - rect1[0]) * (rect1[3] - rect1[1])
        rect2_area = (rect2[2] - rect2[0]) * (rect2[3] - rect2[1])

        # 计算并集面积
        union_area = rect1_area + rect2_area - intersect_area

        # 返回IOU
        return intersect_area / union_area if union_area > 0 else 0.0

    def create_path_rectangle(
        self, start_point: Dict, end_point: Dict, margin: float = None
    ) -> Tuple[float, float, float, float]:
        """
        创建包含起点和终点的矩形，带有一定的边距

        Args:
            start_point: 起点坐标，包含lat和lng键
            end_point: 终点坐标，包含lat和lng键
            margin: 矩形边距，相对于路径长度的比例，如果为None则使用配置文件中的值

        Returns:
            Tuple[float, float, float, float]: 矩形坐标 (y_min, x_min, y_max, x_max)
        """
        # 如果未指定margin，使用配置文件中的值
        if margin is None:
            margin = settings.settings.parallel_processing.path_margin

        # 提取坐标
        start_lat, start_lng = start_point.get("lat", 0), start_point.get("lng", 0)
        end_lat, end_lng = end_point.get("lat", 0), end_point.get("lng", 0)

        # 计算路径长度
        path_length = np.sqrt((end_lat - start_lat) ** 2 + (end_lng - start_lng) ** 2)

        # 计算边距
        margin_distance = path_length * margin

        # 创建矩形
        y_min = min(start_lat, end_lat) - margin_distance
        x_min = min(start_lng, end_lng) - margin_distance
        y_max = max(start_lat, end_lat) + margin_distance
        x_max = max(start_lng, end_lng) + margin_distance

        return (y_min, x_min, y_max, x_max)

    def group_messages(self, messages: List[Dict]) -> List[List[Dict]]:
        """
        将消息分组，使得可能冲突的消息在同一组

        Args:
            messages: 消息列表

        Returns:
            List[List[Dict]]: 分组后的消息列表
        """
        # 如果消息数量小于等于1，直接返回
        if len(messages) <= 1:
            return [messages] if messages else []

        # 提取风险评估消息
        risk_assessment_msgs = []
        other_msgs = []

        for msg in messages:
            if msg.get("desc") == "风险评估":
                risk_assessment_msgs.append(msg)
            else:
                other_msgs.append(msg)

        # 如果没有风险评估消息，所有消息可以并行处理
        if not risk_assessment_msgs:
            return [[msg] for msg in other_msgs] if other_msgs else []

        # 为每个风险评估消息创建信息
        message_info = []
        for i, msg in enumerate(risk_assessment_msgs):
            data = msg.get("data", {})

            # 获取飞行高度
            fly_height = float(data.get("flyHeight", 0))

            # 获取起点和终点
            waypoints = data.get("waypoints", [])
            if len(waypoints) >= 2:
                start_point = waypoints[0]
                end_point = waypoints[-1]

                # 创建路径矩形
                path_rect = self.create_path_rectangle(start_point, end_point)

                message_info.append(
                    {
                        "index": i,
                        "message": msg,
                        "fly_height": fly_height,
                        "path_rect": path_rect,
                    }
                )

        # 创建分组
        groups = []
        remaining = set(range(len(message_info)))

        while remaining:
            # 取出第一个未分组的消息
            current_idx = next(iter(remaining))
            current = message_info[current_idx]

            # 创建新组
            current_group = [current]
            remaining.remove(current_idx)

            # 查找与当前消息可能冲突的其他消息
            for idx in list(remaining):
                other = message_info[idx]

                # 检查高度差
                height_diff = abs(current["fly_height"] - other["fly_height"])

                # 从配置文件获取高度差阈值
                height_diff_threshold = (
                    settings.settings.parallel_processing.height_diff_threshold
                )

                # 如果高度差大于阈值，不会冲突
                if height_diff > height_diff_threshold:
                    continue

                # 检查路径矩形的IOU
                iou = self.calculate_iou(current["path_rect"], other["path_rect"])

                # 从配置文件获取IOU阈值
                iou_threshold = settings.settings.parallel_processing.iou_threshold

                # 如果IOU大于阈值，认为可能冲突
                if iou > iou_threshold:
                    current_group.append(other)
                    remaining.remove(idx)

            # 将组添加到分组列表
            groups.append([item["message"] for item in current_group])

        # 将其他非风险评估消息添加到任意组或创建新组
        for msg in other_msgs:
            if groups:
                # 添加到第一个组
                groups[0].append(msg)
            else:
                # 创建新组
                groups.append([msg])

        return groups

    def process_message_groups(
        self, message_groups: List[List[Dict]], handler_func, max_parallel=None
    ) -> List[Dict]:
        """
        并行处理消息组

        Args:
            message_groups: 分组后的消息列表
            handler_func: 处理单个消息的函数
            max_parallel: 最大并行处理组数，默认为None（使用self.max_workers）

        Returns:
            List[Dict]: 处理结果列表
        """
        if not message_groups:
            return []

        max_parallel = max_parallel or self.max_workers
        results = []

        # 限制并行组数
        for i in range(0, len(message_groups), max_parallel):
            batch = message_groups[i : i + max_parallel]

            # 创建任务
            futures = []
            for group in batch:
                # 每个组内顺序处理
                for msg in group:
                    future = self.executor.submit(handler_func, msg)
                    futures.append((future, msg))

            # 等待所有任务完成
            for future, msg in futures:
                try:
                    result = future.result()
                    results.append({"message": msg, "result": result, "success": True})
                except Exception as e:
                    logger.error(f"处理消息时出错: {str(e)}")
                    results.append(
                        {
                            "message": msg,
                            "result": None,
                            "success": False,
                            "error": str(e),
                        }
                    )

        return results

    def shutdown(self):
        """关闭线程池"""
        self.executor.shutdown(wait=True)
