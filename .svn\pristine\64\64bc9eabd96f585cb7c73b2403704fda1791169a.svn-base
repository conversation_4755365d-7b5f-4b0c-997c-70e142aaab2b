[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://<EMAIL>/liuchang1230/multi-agent-pathfinding.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
[branch "time-safety-margin"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/time-safety-margin
[branch "jump-point-search"]
	vscode-merge-base = origin/time-safety-margin
	remote = origin
	merge = refs/heads/jump-point-search
[remote "multi-agent-pathfinding"]
	url = https://github.com/liuchang1230/multi-agent-pathfinding.git
	fetch = +refs/heads/*:refs/remotes/multi-agent-pathfinding/*
[branch "feature/no-fly-zone-auto-reroute"]
	vscode-merge-base = origin/jump-point-search
[branch "feature/codebase-refactor"]
	vscode-merge-base = origin/main
