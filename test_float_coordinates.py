#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浮点数坐标的轨道路径规划算法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import math
import matplotlib.pyplot as plt
import matplotlib.patches as patches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建一个模拟的地图类用于测试
class MockMap3D:
    def __init__(self):
        self.height = 1000
        self.width = 1000
        self.depth = 100
        
        # 模拟障碍物管理器
        self.obstacle_manager = MockObstacleManager()
        
        # 模拟网格转换器
        self.grid_converter = MockGridConverter()
        
        # 模拟不可通行点集合 - 创建一个圆形禁飞区
        self.non_traversable = set()
        
        # 禁飞区: 圆形
        center = (500, 400)
        radius = 80
        for y in range(center[0] - radius, center[0] + radius):
            for x in range(center[1] - radius, center[1] + radius):
                if (y - center[0])**2 + (x - center[1])**2 <= radius**2:
                    for z in range(0, 80):
                        self.non_traversable.add((y, x, z))
    
    def traversable(self, y, x, z):
        return (y, x, z) not in self.non_traversable

class MockObstacleManager:
    def __init__(self):
        self.orbit_paths = {}
        self.orbit_quadrants = {}
        
        # 创建禁飞区的轨道 (圆形)
        center = (500, 400)
        radius = 90  # 比禁飞区稍大
        orbit = []
        for i in range(32):  # 32个轨道点
            angle = 2 * math.pi * i / 32
            y = int(center[0] + radius * math.cos(angle))
            x = int(center[1] + radius * math.sin(angle))
            orbit.append((y, x))
        
        self.orbit_paths["test_zone"] = orbit
        self.orbit_quadrants["test_zone"] = {
            "N": set(range(0, 4)),
            "NE": set(range(4, 8)),
            "E": set(range(8, 12)),
            "SE": set(range(12, 16)),
            "S": set(range(16, 20)),
            "SW": set(range(20, 24)),
            "W": set(range(24, 28)),
            "NW": set(range(28, 32))
        }
    
    def get_type_at_position(self, pos):
        y, x, z = pos
        
        # 检查禁飞区 (圆形)
        center = (500, 400)
        if (y - center[0])**2 + (x - center[1])**2 <= 80**2:
            return ["test_zone"]
        
        return []
    
    def get_orbit_path(self, zone_name):
        return self.orbit_paths.get(zone_name, [])
    
    def get_orbit_quadrants(self, zone_name):
        return self.orbit_quadrants.get(zone_name, {})

class MockGridConverter:
    def relative_to_geo(self, y, x, z):
        return {"lat": 39.0 + y * 0.001, "lon": 116.0 + x * 0.001, "alt": z * 10}

def test_float_coordinates():
    """测试浮点数坐标的直线移动"""
    print("开始测试浮点数坐标的轨道路径规划...")
    
    # 导入轨道路径规划器
    from src.core.pathfinding.orbit import OrbitPathFinder
    
    # 创建模拟地图
    mock_map = MockMap3D()
    
    # 创建轨道路径规划器
    orbit_finder = OrbitPathFinder(mock_map)
    
    # 测试场景：需要绕过圆形禁飞区的对角线路径
    test_cases = [
        {
            "name": "对角线路径测试",
            "start": (200, 200, 50),
            "goal": (800, 600, 50),  # 对角线目标
            "min_height": 50
        },
        {
            "name": "精确角度路径测试", 
            "start": (100, 300, 50),
            "goal": (900, 500, 50),  # 需要精确角度的路径
            "min_height": 50
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases):
        print(f"\n测试 {test_case['name']}")
        
        # 执行路径规划
        path, error = orbit_finder.find_path(
            start=test_case["start"],
            goal=test_case["goal"],
            min_height=test_case["min_height"],
            agent_id=f"drone_{i}",
            start_time=0
        )
        
        if path:
            print(f"✓ 路径规划成功，路径长度: {len(path)} 个节点")
            
            # 分析路径的平滑度
            analyze_path_smoothness(path, test_case["name"])
            
            results.append({
                "case": test_case,
                "path": path,
                "success": True
            })
        else:
            print(f"✗ 路径规划失败: {error}")
            results.append({
                "case": test_case,
                "path": None,
                "success": False
            })
    
    # 可视化结果
    visualize_float_coordinate_results(mock_map, results)
    
    return results

def analyze_path_smoothness(path, case_name):
    """分析路径的平滑度"""
    if len(path) < 3:
        print(f"  路径太短，无法分析平滑度")
        return
    
    # 计算路径中的转向角度
    angles = []
    for i in range(1, len(path) - 1):
        prev_node = path[i-1]
        curr_node = path[i]
        next_node = path[i+1]
        
        # 计算两个向量
        vec1 = (curr_node.y - prev_node.y, curr_node.x - prev_node.x)
        vec2 = (next_node.y - curr_node.y, next_node.x - curr_node.x)
        
        # 计算角度
        if vec1 != (0, 0) and vec2 != (0, 0):
            dot_product = vec1[0] * vec2[0] + vec1[1] * vec2[1]
            mag1 = math.sqrt(vec1[0]**2 + vec1[1]**2)
            mag2 = math.sqrt(vec2[0]**2 + vec2[1]**2)
            
            cos_angle = dot_product / (mag1 * mag2)
            cos_angle = max(-1, min(1, cos_angle))  # 限制在[-1, 1]范围内
            angle = math.acos(cos_angle)
            angles.append(math.degrees(angle))
    
    if angles:
        avg_angle = sum(angles) / len(angles)
        max_angle = max(angles)
        sharp_turns = sum(1 for angle in angles if angle > 90)
        
        print(f"  路径平滑度分析:")
        print(f"    平均转向角度: {avg_angle:.2f}°")
        print(f"    最大转向角度: {max_angle:.2f}°")
        print(f"    急转弯次数 (>90°): {sharp_turns}")
        print(f"    路径平滑度评分: {100 - min(100, avg_angle):.1f}/100")

def visualize_float_coordinate_results(mock_map, results):
    """可视化浮点数坐标测试结果"""
    fig, axes = plt.subplots(1, len(results), figsize=(6*len(results), 6))
    if len(results) == 1:
        axes = [axes]
    
    for idx, result in enumerate(results):
        ax = axes[idx]
        
        # 绘制禁飞区
        center = (400, 500)  # 注意坐标转换 (y,x) -> (x,y)
        circle = patches.Circle(center, 80, linewidth=2, edgecolor='red', facecolor='lightcoral', alpha=0.7)
        ax.add_patch(circle)
        ax.text(center[0], center[1], '禁飞区', ha='center', va='center', fontsize=10, fontweight='bold')
        
        # 绘制轨道
        orbit_points = mock_map.obstacle_manager.get_orbit_path("test_zone")
        if orbit_points:
            orbit_x = [point[1] for point in orbit_points]  # 转换坐标
            orbit_y = [point[0] for point in orbit_points]
            orbit_x.append(orbit_x[0])
            orbit_y.append(orbit_y[0])
            
            ax.plot(orbit_x, orbit_y, '--', color='blue', linewidth=1, alpha=0.5, label='轨道')
        
        if result["success"] and result["path"]:
            path = result["path"]
            case = result["case"]
            
            # 绘制路径
            path_x = [node.x for node in path]
            path_y = [node.y for node in path]
            
            ax.plot(path_x, path_y, 'o-', color='green', linewidth=2, markersize=3, 
                   label='规划路径', alpha=0.8)
            
            # 标记起点和终点
            ax.plot(case["start"][1], case["start"][0], 's', color='blue', markersize=10, 
                   label='起点', markeredgecolor='black', markeredgewidth=1)
            ax.plot(case["goal"][1], case["goal"][0], '^', color='red', markersize=10, 
                   label='终点', markeredgecolor='black', markeredgewidth=1)
            
            # 绘制理想直线路径（用于对比）
            ax.plot([case["start"][1], case["goal"][1]], [case["start"][0], case["goal"][0]], 
                   '--', color='orange', linewidth=1, alpha=0.7, label='理想直线')
            
            # 添加路径信息
            path_length = len(path)
            direct_distance = math.sqrt(
                (case["goal"][0] - case["start"][0])**2 + 
                (case["goal"][1] - case["start"][1])**2
            )
            
            # 计算实际路径长度
            actual_distance = 0
            for i in range(1, len(path)):
                actual_distance += math.sqrt(
                    (path[i].y - path[i-1].y)**2 + 
                    (path[i].x - path[i-1].x)**2
                )
            
            efficiency = (direct_distance / actual_distance) * 100 if actual_distance > 0 else 0
            
            info_text = f"""路径信息:
节点数: {path_length}
直线距离: {direct_distance:.1f}
实际距离: {actual_distance:.1f}
路径效率: {efficiency:.1f}%"""
            
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=9,
                   verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.8))
        
        ax.set_xlim(0, 1000)
        ax.set_ylim(0, 1000)
        ax.set_xlabel('X坐标')
        ax.set_ylabel('Y坐标')
        ax.set_title(f'{result["case"]["name"]}')
        ax.legend(loc='upper right')
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
    
    plt.tight_layout()
    filename = 'float_coordinate_test_results.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"\n可视化结果已保存为 '{filename}'")
    plt.close()

def main():
    """主函数"""
    print("=" * 60)
    print("浮点数坐标轨道路径规划测试")
    print("=" * 60)
    
    # 测试浮点数坐标功能
    results = test_float_coordinates()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    success_count = sum(1 for r in results if r["success"])
    print(f"成功测试: {success_count}/{len(results)}")
    
    if success_count == len(results):
        print("✓ 所有测试通过！浮点数坐标功能正常工作。")
    else:
        print("✗ 部分测试失败，请检查代码。")
    print("=" * 60)

if __name__ == "__main__":
    main()
