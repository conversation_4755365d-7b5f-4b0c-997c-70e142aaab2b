# 轨道路径规划算法实现说明

## 概述

本文档描述了基于轨道的无人机路径规划算法的实现。该算法将路径规划分为三个阶段：起飞、巡航和降落，其中巡航阶段采用轨道避障策略来处理禁飞区。

## 算法特点

### 1. 三阶段路径规划
- **起飞阶段**：垂直上升到最小巡航高度
- **巡航阶段**：使用轨道避障策略在固定高度层移动
- **降落阶段**：垂直下降到目标点

### 2. 轨道避障策略
- 直线朝目标移动，遇到禁飞区时进入轨道
- 基于象限判断选择最优轨道移动方向
- 支持重叠禁飞区的轨道切换
- 防止死循环的机制

## 核心算法流程

### 巡航阶段详细流程

1. **直线移动**
   - 无人机直线朝着巡航终点移动
   - 每一步检查是否遇到禁飞区

2. **进入轨道**
   - 遇到禁飞区时，搜索该禁飞区的轨道点
   - 选择距离当前位置最近的轨道点作为入口
   - 计算巡航终点在轨道序列中的象限位置

3. **象限确定**
   - 确定目标象限和相邻两个象限（共三个象限）
   - 基于距离计算选择轨道移动方向（顺时针或逆时针）

4. **轨道移动**
   - 沿着选定方向在轨道上移动
   - 检查是否进入了其他禁飞区（处理重叠情况）
   - 如果进入新禁飞区，切换到新轨道

5. **退出轨道**
   - 到达目标象限时，检查是否可以直线移动到目标
   - 如果可行，退出轨道继续直线移动
   - 如果不可行，继续沿轨道移动

6. **迭代直到到达目标**

## 关键技术实现

### 1. 轨道点生成
- 圆形禁飞区：生成圆形轨道点序列
- 多边形禁飞区：沿多边形边界生成轨道点序列
- 轨道点按8个象限分类（N, NE, E, SE, S, SW, W, NW）

### 2. 象限判断
```python
def _determine_target_quadrants(self, cruise_goal, zone_name):
    # 找到距离目标最近的轨道点所在象限
    # 返回目标象限及其相邻象限
```

### 3. 方向选择
```python
def _choose_orbit_direction(self, entry_point_idx, target_quadrants, zone_name, cruise_goal):
    # 计算顺时针和逆时针到达目标象限的距离
    # 选择距离更短的方向
```

### 4. 死循环防止
- 记录访问过的禁飞区
- 限制轨道切换次数
- 设置最大迭代次数

## 代码结构

### 主要类：OrbitPathFinder

**核心方法：**
- `find_path()`: 主路径规划接口
- `_find_cruise_path()`: 巡航路径规划
- `_vertical_takeoff()`: 起飞阶段
- `_vertical_landing()`: 降落阶段

**辅助方法：**
- `_is_valid_position()`: 位置有效性检查
- `_get_obstacle_at_position()`: 获取位置处的禁飞区
- `_find_nearby_orbit_points()`: 搜索附近轨道点
- `_determine_target_quadrants()`: 确定目标象限
- `_choose_orbit_direction()`: 选择轨道方向
- `_can_exit_orbit_to_goal()`: 检查是否可退出轨道
- `_move_along_orbit()`: 沿轨道移动
- `_direct_move_towards_goal()`: 直线移动

## 算法优势

1. **高效避障**：通过轨道策略有效避开禁飞区
2. **路径平滑**：减少急转弯，提高飞行安全性
3. **处理复杂场景**：支持重叠禁飞区和复杂几何形状
4. **防止死循环**：内置多种机制防止算法陷入死循环
5. **接口兼容**：与现有JPS算法保持相同接口

## 使用示例

```python
from src.core.pathfinding.orbit import OrbitPathFinder

# 创建轨道路径规划器
orbit_finder = OrbitPathFinder(map3d)

# 执行路径规划
path, error = orbit_finder.find_path(
    start=(100, 100, 5),
    goal=(800, 800, 5),
    min_height=50,
    agent_id="drone_1",
    start_time=0
)

if path:
    print(f"路径规划成功，路径长度: {len(path)}")
else:
    print(f"路径规划失败: {error}")
```

## 测试验证

实现了完整的测试套件：
- 基本功能测试
- 轨道相关方法测试
- 简单路径规划测试
- 模拟环境测试

所有测试均通过，验证了算法的正确性和稳定性。

## 配置参数

- `orbit_search_radius`: 轨道点搜索半径（默认10）
- `max_orbit_iterations`: 最大轨道迭代次数（默认100）
- `max_orbit_switches`: 最大轨道切换次数（默认3）
- 速度参数：起飞、巡航、降落速度时间间隔

## 总结

本轨道路径规划算法成功实现了您提出的设计思路，具有以下特点：
- 三阶段路径规划结构清晰
- 轨道避障策略有效
- 象限判断和方向选择合理
- 重叠禁飞区处理完善
- 死循环防止机制可靠

算法已通过测试验证，可以投入实际使用。
