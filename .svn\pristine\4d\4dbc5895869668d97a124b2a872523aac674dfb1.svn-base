import sys
import os
import argparse
from pathlib import Path

# .env loading logic removed as config is now handled by config.json and settings.py

# 确保当前工作目录正确 (Keep this part if necessary for relative paths)
os.chdir(os.path.dirname(os.path.abspath(__file__)))

# 然后再导入其他模块
from src.utils.logging import setup_logging
from src.config.settings import initialize_settings
from src.handlers.kafka_consumer import PathPlanningConsumer
from src.handlers.risk_assessment_consumer import RiskAssessmentConsumer
from src.handlers.message_handlers.base import MessageHandler
from src.core.validation.route_validator import RouteValidator

VERSION = "v 0.0.1"


def main():
    # 设置日志
    setup_logging()

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="路径规划服务")
    parser.add_argument(
        "-v", "--version", action="version", version=f"%(prog)s {VERSION}"
    )
    parser.add_argument(
        "--mode",
        choices=["normal", "risk"],
        default="risk",
        help="运行模式: normal(普通路径规划) or risk(风险评估)",
    )
    parser.add_argument(
        "--location",
        type=str,
        help="指定地点 (例如: nanjing, shijiazhuang)，不指定则使用配置文件中的值",
    )
    parser.add_argument(
        "--server",
        type=str,
        help="指定服务器地址 (例如: ***********)，不指定则使用配置文件中的值",
    )
    parser.add_argument(
        "--config",
        type=str,
        help="配置文件路径，默认为当前目录下的config.json",
    )
    args = parser.parse_args()

    # 初始化配置 (必须在 setup_logging 之后，在使用 settings 的模块导入之前完成)
    # 注意：虽然导入发生在文件顶部，但实际的 settings 实例现在在这里创建
    config_path = Path(args.config) if args.config else None
    initialize_settings(
        config_path=config_path, location=args.location, server_address=args.server
    )

    # 在系统启动时初始化数据库连接
    # print("初始化数据库连接...")
    MessageHandler.initialize_connections()

    try:
        if args.mode == "risk":
            # 风险评估模式
            consumer = RiskAssessmentConsumer()
        else:
            # 普通路径规划模式
            consumer = PathPlanningConsumer()

        consumer.start_consuming()
    except KeyboardInterrupt:
        print("\n正在关闭服务...")
        # 关闭消费者连接和定时器
        if "consumer" in locals():
            consumer.close()

        # 关闭所有数据库和定时器连接
        print("正在关闭数据库连接...")
        MessageHandler.close_connections()
        RouteValidator.close_connections()

        print("服务已安全关闭")
        sys.exit(0)
    except Exception as e:
        print(f"服务运行出错: {str(e)}")
        # 尝试关闭所有连接和定时器
        try:
            if "consumer" in locals():
                consumer.close()
            MessageHandler.close_connections()
            RouteValidator.close_connections()
        except:
            pass
        sys.exit(1)


if __name__ == "__main__":
    main()
