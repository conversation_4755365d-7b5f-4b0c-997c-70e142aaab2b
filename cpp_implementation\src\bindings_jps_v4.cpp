#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/stl_optional.h>
#include "grid_node.h"      // For GridNode type
#include "occupancy_map.h"  // For OccupancyMap type
#include "grid_node_data.h" // For Constraint type
#include "optimized_path_finder.h"

namespace py = pybind11;

// It's important that the module defining GridNode, OccupancyMap, Constraint
// (i.e., occupancy_map_cpp) is imported in Python before this module,
// or that these types are fully defined here if not relying on cross-module types.
// Pybind11 can handle types defined in other modules if they are registered.

PYBIND11_MODULE(jps_v4_cpp, m)
{
    m.doc() = "C++ Optimized JPS v4 Path Finder";

    // Ensure dependent types from occupancy_map_cpp are known.
    // This can be done by importing the other module in Python first,
    // or by using py::module_::import("occupancy_map_cpp"); here,
    // but the former is generally safer and more common.

    py::class_<OptimizedPathFinder>(m, "OptimizedPathFinder")
        .def(py::init<const OccupancyMap &, double, double, double, bool, int, bool, double, int>(),
             py::arg("map"),
             py::arg("takeoff_speed"),
             py::arg("cruise_speed"),
             py::arg("landing_speed"),
             py::arg("only_turning_points"),
             py::arg("max_jps_steps"),
             py::arg("need_smooth"),
             py::arg("smoothness_factor"),
             py::arg("cache_size") = 10000,
             py::keep_alive<1, 2>()) // Keep map alive
        .def("find_path", &OptimizedPathFinder::find_path,
             py::arg("start_node"),
             py::arg("goal_node"),
             py::arg("min_height"),
             py::arg("agent_id"),
             py::arg("constraints"));
    // The return type std::pair<std::pair<std::vector<GridNode>, std::vector<GridNode>>, std::optional<std::string>>
    // will be automatically converted by pybind11.
    // GridNode, Constraint should be recognized from the occupancy_map_cpp module if imported first.
}
