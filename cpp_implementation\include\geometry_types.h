#ifndef GEOMETRY_TYPES_H
#define GEOMETRY_TYPES_H

#include <vector>
#include <limits> // Required for std::numeric_limits

namespace CVToolbox
{ // Using a more generic namespace for geometry tools

    // Basic 2D Point structure
    struct Point2D
    {
        double y;
        double x;

        Point2D(double y_val = 0.0, double x_val = 0.0) : y(y_val), x(x_val) {}

        // Basic vector operations if needed later
        Point2D operator+(const Point2D &other) const
        {
            return Point2D(y + other.y, x + other.x);
        }
        Point2D operator-(const Point2D &other) const
        {
            return Point2D(y - other.y, x - other.x);
        }
        Point2D operator*(double scalar) const
        {
            return Point2D(y * scalar, x * scalar);
        }
        // Dot product
        double dot(const Point2D &other) const
        {
            return y * other.y + x * other.x;
        }
        // Cross product (2D: returns scalar z-component of 3D cross product)
        double cross(const Point2D &other) const
        {
            return y * other.x - x * other.y;
        }
        double length_sq() const
        {
            return y * y + x * x;
        }
    };

    // Polygon represented as a list of 2D points
    using Polygon2D = std::vector<Point2D>;

    // Basic 3D Point structure (can be extended from GridNodeData if only y,x,z,t are needed)
    // For now, a simple one for geometric calculations if MapHandler3D needs it.
    // GridNodeData is more for occupancy map's specific needs.
    struct Point3D
    {
        double y;
        double x;
        double z;

        Point3D(double y_val = 0.0, double x_val = 0.0, double z_val = 0.0)
            : y(y_val), x(x_val), z(z_val) {}
    };

    // A structure to hold bounding box for a polygon
    struct BoundingBox2D
    {
        double min_y = std::numeric_limits<double>::max();
        double max_y = std::numeric_limits<double>::lowest();
        double min_x = std::numeric_limits<double>::max();
        double max_x = std::numeric_limits<double>::lowest();

        BoundingBox2D() = default;

        BoundingBox2D(const Polygon2D &polygon)
        {
            if (polygon.empty())
                return;
            for (const auto &p : polygon)
            {
                if (p.y < min_y)
                    min_y = p.y;
                if (p.y > max_y)
                    max_y = p.y;
                if (p.x < min_x)
                    min_x = p.x;
                if (p.x > max_x)
                    max_x = p.x;
            }
        }

        bool contains(const Point2D &p) const
        {
            return p.y >= min_y && p.y <= max_y && p.x >= min_x && p.x <= max_x;
        }
    };

} // namespace CVToolbox
#endif // GEOMETRY_TYPES_H
