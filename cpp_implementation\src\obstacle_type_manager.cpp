#include "obstacle_type_manager.h" // Should be "../include/obstacle_type_manager.h"

namespace CVToolbox
{

    ObstacleTypeManagerCpp::ObstacleTypeManagerCpp(int map_height, int map_width, int map_depth)
        : map_height_(map_height), map_width_(map_width), map_depth_(map_depth)
    {
        if (map_height <= 0 || map_width <= 0 || map_depth <= 0)
        {
            throw std::invalid_argument("Map dimensions must be positive.");
        }
    }

    bool ObstacleTypeManagerCpp::is_in_bounds(const GridPoint3D &position) const
    {
        const auto &[y, x, z] = position;
        return (y >= 0 && y < map_height_ &&
                x >= 0 && x < map_width_ &&
                z >= 0 && z < map_depth_);
    }

    bool ObstacleTypeManagerCpp::register_type(const std::string &type_name, const std::string &description)
    {
        if (types_.count(type_name))
        {
            return false; // Type already exists
        }
        types_[type_name] = {description};
        positions_by_type_[type_name] = {}; // Initialize with an empty set of positions
        return true;
    }

    bool ObstacleTypeManagerCpp::unregister_type(const std::string &type_name, std::set<GridPoint3D> *out_removed_positions)
    {
        if (!types_.count(type_name))
        {
            return false; // Type not found
        }
        types_.erase(type_name);

        auto it = positions_by_type_.find(type_name);
        if (it != positions_by_type_.end())
        {
            if (out_removed_positions)
            {
                *out_removed_positions = std::move(it->second); // Efficiently move the set
            }
            positions_by_type_.erase(it);
        }
        return true;
    }

    bool ObstacleTypeManagerCpp::add_position(const std::string &type_name, const GridPoint3D &position)
    {
        if (!types_.count(type_name))
        {
            return false; // Type not registered
        }
        // Optional: Check bounds before adding
        if (!is_in_bounds(position))
        {
            // Or log a warning, or throw std::out_of_range("Position out of bounds");
            return false; // Position out of bounds, do not add
        }
        positions_by_type_[type_name].insert(position);
        return true;
    }

    bool ObstacleTypeManagerCpp::add_positions_batch(const std::string &type_name, const std::vector<GridPoint3D> &positions)
    {
        if (!types_.count(type_name))
        {
            return false; // Type not registered
        }
        auto &pos_set = positions_by_type_[type_name];
        for (const auto &pos : positions)
        {
            if (is_in_bounds(pos))
            { // Check bounds for each position
                pos_set.insert(pos);
            }
            else
            {
                // Optionally log a warning for out-of-bounds positions in batch
            }
        }
        return true;
    }

    bool ObstacleTypeManagerCpp::remove_position(const std::string &type_name, const GridPoint3D &position)
    {
        auto it = positions_by_type_.find(type_name);
        if (it == positions_by_type_.end())
        {
            return false; // Type not found
        }
        return it->second.erase(position) > 0;
    }

    std::set<GridPoint3D> ObstacleTypeManagerCpp::clear_all_positions(const std::string &type_name)
    {
        auto it = positions_by_type_.find(type_name);
        if (it == positions_by_type_.end())
        {
            return {}; // Type not found, return empty set
        }
        std::set<GridPoint3D> cleared_positions = std::move(it->second);
        it->second.clear(); // Ensure the set in the map is empty
        return cleared_positions;
    }

    std::vector<std::string> ObstacleTypeManagerCpp::get_type_at_position(const GridPoint3D &position) const
    {
        std::vector<std::string> types_at_pos;
        if (!is_in_bounds(position))
        { // Optional: return empty if out of bounds
            return types_at_pos;
        }
        for (const auto &pair : positions_by_type_)
        {
            if (pair.second.count(position))
            {
                types_at_pos.push_back(pair.first);
            }
        }
        return types_at_pos;
    }

    std::map<std::string, ObstacleTypeInfo> ObstacleTypeManagerCpp::get_all_types() const
    {
        return types_;
    }

    std::set<GridPoint3D> ObstacleTypeManagerCpp::get_positions_set(const std::string &type_name) const
    {
        auto it = positions_by_type_.find(type_name);
        if (it == positions_by_type_.end())
        {
            return {}; // Type not found, return empty set
        }
        return it->second;
    }

    bool ObstacleTypeManagerCpp::is_type_registered(const std::string &type_name) const
    {
        return types_.count(type_name) > 0;
    }

} // namespace CVToolbox
