# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src/core/map/cache/chongqing/fixed_obstacles_cache.npz', 'cache/chongqing'),
    ],
    hiddenimports=[
        'kafka',
        'redis',
        'numpy',
        'numpy.core._multiarray_umath',
        'numpy.core._umath_tests',
        'mysql.connector',
        'paho.mqtt',
        'termcolor',
        'dotenv',
        'matplotlib',
        'matplotlib.backends.backend_tkagg',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 收集matplotlib的数据文件
from PyInstaller.utils.hooks import collect_data_files
matplotlib_datas = collect_data_files('matplotlib')
a.datas.extend(matplotlib_datas)

pyz = PYZ(
    a.pure,
    a.zipped_data,
    cipher=block_cipher
)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='route_planning_service',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
