from typing import Dict, List, Optional, Tuple
from datetime import datetime
import time
import random
from concurrent.futures import ThreadPoolExecutor, TimeoutError

from ...utils.logging import get_logger

# from ...core.pathfinding.high_level_policy_3d import CBS3D
from .base import MessageHandler
from ...config import settings  # Corrected import

logger = get_logger(__name__)


# 添加性能计时装饰器
def timing_decorator(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        logger.info(f"性能统计 - {func.__name__} 执行时间: {execution_time:.4f} 秒")
        return result

    return wrapper


def generate_unique_id():
    """生成唯一ID：时间戳+随机数"""
    timestamp = int(time.time() * 1000)  # 毫秒级时间戳
    random_num = random.randint(1000, 9999)
    return timestamp * 10000 + random_num  # 组合成更长的数字


class PlanningMessageHandler(MessageHandler):
    """处理新路径规划消息"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 创建线程池用于异步数据库操作
        self.executor = ThreadPoolExecutor(max_workers=2)

    @timing_decorator
    def handle_message(self, message: Dict, needsend=True):
        """
        处理路径规划消息

        Args:
            message: 消息字典，包含规划请求信息

        Returns:
            Tuple[Optional[List], Optional[str]]: (路径点列表, 错误信息)
        """
        try:
            # 记录总体开始时间
            total_start_time = time.time()

            message_data = message.get("data", {})
            flight_id = message_data["flightapplyid"]
            # 解析基本参数
            begin_time = self.parse_time(message_data["beginTime"])

            # 从占用图中删除旧路径  （可能在执行前新建禁飞区了，所以需要重新改航）
            remove_start_time = time.time()
            self.occupancy_map.remove_agent(flight_id)
            logger.info(
                f"性能统计 - 删除旧路径耗时: {time.time() - remove_start_time:.4f} 秒"
            )

            # 验证起飞时间
            current_time = time.time()
            if begin_time < current_time:
                new_data = {
                    "risk_state": False,
                    "risk_reason": "起飞时间早于当前时间",
                }
                return (message_data, new_data), "起飞时间早于当前时间"

            # 解析起飞点和降落点
            parse_points_start_time = time.time()
            start_point = self.parse_point(message_data["takeOffPoint"])
            end_point = self.parse_point(message_data["landingPoint"])
            logger.info(
                f"性能统计 - 解析起降点耗时: {time.time() - parse_points_start_time:.4f} 秒"
            )

            min_cruise_alt = float(message_data["flyHeight"])

            if (
                min_cruise_alt < settings.settings.map.min_cruise_alt
                or min_cruise_alt > settings.settings.map.max_cruise_alt
            ):
                new_data = {
                    "risk_state": False,
                    "risk_reason": "巡航高度不在范围内",
                }
                return (message_data, new_data), "巡航高度不在范围内"

            # 坐标转换开始时间
            coord_convert_start_time = time.time()

            # 转换成相对高度
            relative_min_cruise_alt = self.grid_converter.height_to_relative(
                min_cruise_alt
            )

            # 转换网格坐标
            start_grid = self.grid_converter.geo_to_relative(
                start_point["lat"],
                start_point["lon"],
                start_point["alt"],
            )
            end_grid = self.grid_converter.geo_to_relative(
                end_point["lat"],
                end_point["lon"],
                end_point["alt"],
            )

            logger.info(
                f"性能统计 - 坐标转换耗时: {time.time() - coord_convert_start_time:.4f} 秒"
            )

            if (
                start_grid[0] < 0
                or start_grid[1] < 0
                or end_grid[0] < 0
                or end_grid[1] < 0
                or start_grid[0] >= settings.settings.map.width  # Corrected access
                or start_grid[1] >= settings.settings.map.height  # Corrected access
                or end_grid[0] >= settings.settings.map.width  # Corrected access
                or end_grid[1] >= settings.settings.map.height  # Corrected access
            ):
                new_data = {
                    "risk_state": False,
                    "risk_reason": "起飞或降落点不在地图范围内",
                }
                return (message_data, new_data), "起飞或降落点不在地图范围内"

            # ===> Start Point NFZ Check <===
            nfz_check_start_time = time.time()
            start_y, start_x, _ = start_grid  # Extract Y, X coordinates
            # Ensure coordinates are integers for the check
            start_yx_int = (int(round(start_y)), int(round(start_x)))
            conflicting_nfz = self.map.is_point_inside_any_nfz_2d(start_yx_int)
            if conflicting_nfz:
                error_msg = f"起飞点位于禁飞区 '{conflicting_nfz}' 内部"
                logger.error(f"{error_msg} - 申请ID: {flight_id}")
                new_data = {
                    "risk_state": False,
                    "risk_reason": error_msg,
                }

                return (message_data, new_data), error_msg

            # ===> End Point NFZ Check <===
            end_y, end_x, _ = end_grid  # Extract Y, X coordinates
            # Ensure coordinates are integers for the check
            end_yx_int = (int(round(end_y)), int(round(end_x)))
            conflicting_nfz_end = self.map.is_point_inside_any_nfz_2d(end_yx_int)
            if conflicting_nfz_end:
                error_msg = f"降落点位于禁飞区 '{conflicting_nfz_end}' 内部"
                logger.error(f"{error_msg} - 申请ID: {flight_id}")
                new_data = {
                    "risk_state": False,
                    "risk_reason": error_msg,
                }
                return (message_data, new_data), error_msg
            logger.info(
                f"性能统计 - 禁飞区检查耗时: {time.time() - nfz_check_start_time:.4f} 秒"
            )
            # ===> End of End Point NFZ Check <===
            # ===> End of Start Point NFZ Check <===

            # 规划路径（添加5秒超时限制）
            # try:
            #     future = self.executor.submit(
            #         self.planner.solve,
            #         [start_grid],
            #         [end_grid],
            #         relative_min_cruise_alt,
            #         agent_ids=[flight_id],
            #         start_times=[begin_time],
            #     )
            #     solution, error = future.result(timeout=5.0)
            # except TimeoutError:
            #     logger.warning(
            #         f"路径规划超时 - 申请ID: {message_data['flightapplyid']}"
            #     )
            #     new_data = {
            #         "risk_state": False,
            #         "risk_reason": "路径规划超时(超过10秒)",
            #     }
            #     return (message_data, new_data), "路径规划超时"
            # except Exception as e:
            #     logger.error(f"路径规划异常: {str(e)}")
            #     new_data = {
            #         "risk_state": False,
            #         "risk_reason": f"路径规划异常: {str(e)}",
            #     }
            #     return (message_data, new_data), str(e)

            # 路径规划开始时间
            planning_start_time = time.time()
            solution, error = self.planner.solve(
                [start_grid],
                [end_grid],
                relative_min_cruise_alt,
                agent_ids=[flight_id],
                start_times=[begin_time],
            )
            logger.info(
                f"性能统计 - 路径规划耗时: {time.time() - planning_start_time:.4f} 秒"
            )

            if solution:
                path_nodes = solution[flight_id][0]  # node
                turn_path_nodes = solution[flight_id][1]  # node

                # 坐标转换开始时间
                geo_convert_start_time = time.time()
                turn_path_geo = []  # 胡工格式
                turn_path_geo_hugong = []  # 保存到mysql中的格式，以及发给贾博的格式
                base_height = 0
                for i, node in enumerate(turn_path_nodes):
                    coords = self.grid_converter.relative_to_geo(node.y, node.x, node.z)
                    turn_path_geo_hugong.append(
                        [
                            i,
                            coords["lon"],
                            coords["lat"],
                            coords["alt"] - base_height,
                        ]
                    )
                    turn_path_geo.append(
                        {
                            "index": i,
                            "lng": coords["lon"],
                            "lat": coords["lat"],
                            "height": coords["alt"] - base_height,
                            "time": node.t,  # 添加时间信息
                        }
                    )

                    base_height = start_point["alt"]

                logger.info(
                    f"性能统计 - 坐标反向转换耗时: {time.time() - geo_convert_start_time:.4f} 秒"
                )

                # 计算结束时间
                end_time_str = datetime.fromtimestamp(path_nodes[-1].t).strftime(
                    "%Y-%m-%d %H:%M:%S"
                )

                # 准备数据库存储参数
                is_update = bool(message_data.get("uavrootId"))
                route_id = (
                    int(message_data["uavrootId"])
                    if is_update
                    else generate_unique_id()
                )
                # route_name = f"自动规划路径{route_id}"
                route_name = message_data["uavroot"]

                # 计算预计时间（秒）
                estimated_time = int(path_nodes[-1].t - path_nodes[0].t)
                # 计算总距离（点数 * 5米）
                all_distance = len(path_nodes) * 5

                # 异步执行数据库操作
                db_start_time = time.time()
                self.executor.submit(
                    self._save_to_db,
                    route_id,
                    route_name,
                    estimated_time,
                    all_distance,
                    turn_path_geo[1:],
                    is_update,
                )
                logger.info(
                    f"性能统计 - 数据库操作提交耗时: {time.time() - db_start_time:.4f} 秒"
                )

                # self._save_to_db(
                #     route_id,
                #     route_name,
                #     estimated_time,
                #     all_distance,
                #     turn_path_geo[1:],
                #     is_update,
                # )

                # 可视化路径
                # self.visualize_paths()

                new_data = {
                    "uavrootId": str(route_id),  # 转为字符串以确保JSON序列化
                    "uavroot": route_name,
                    "landing_time": end_time_str,
                    # "planned_path_points": turn_path_geo,
                    "adjustment_count": 0,
                    "risk_state": True,
                    "risk_reason": "航线自动路径规划成功",
                }

                # 直接给无人机发送成功响应
                self._send_response(
                    request=message_data,
                    new_data={
                        **new_data,
                        "planned_path_points": turn_path_geo[1:],
                        "changeroute": False,
                    },
                    response_topic=self.uav_topic,
                )
                # 发拐点(给胡工)
                self._send_response(
                    request=message_data,
                    new_data={
                        **new_data,
                        "takeoff_time": message_data["beginTime"],
                        "planned_path_points": turn_path_geo_hugong,
                        "changeroute": False,
                    },
                    response_topic="idspace/wayline/sim",
                )
                logger.info(
                    f"路径规划成功, 路径点总数量：{len(path_nodes)}, 拐点总数量：{len(turn_path_nodes)}"
                )

                # 存储路径（包含node路径）
                store_path_start_time = time.time()
                self.store_path(
                    flight_id,
                    path_nodes,  # 完整node路径列表
                    turn_path_nodes,  # 拐点node路径列表
                    message,
                )
                logger.info(
                    f"性能统计 - 存储路径耗时: {time.time() - store_path_start_time:.4f} 秒"
                )

                # 添加到占用图
                occupancy_start_time = time.time()
                self.occupancy_map.add_path(path_nodes, flight_id)
                logger.info(
                    f"性能统计 - 占用图更新耗时: {time.time() - occupancy_start_time:.4f} 秒"
                )

                # 输出总体执行时间
                logger.info(
                    f"性能统计 - 总体执行时间: {time.time() - total_start_time:.4f} 秒"
                )

                return (message_data, new_data), None

            # 发送失败响应
            new_data = {
                "risk_state": False,
                "risk_reason": error or "路径规划失败",
            }
            if needsend:
                self._send_response(
                    request=message_data,
                    new_data=new_data,
                    response_topic=self.response_topic,
                )

            # 输出总体执行时间（失败情况）
            logger.info(
                f"性能统计 - 总体执行时间(失败): {time.time() - total_start_time:.4f} 秒"
            )

            return (message_data, new_data), error

        except Exception as e:
            error_msg = f"处理路径规划消息时出错: {str(e)}"
            logger.error(error_msg)

            # 输出总体执行时间（异常情况）
            try:
                logger.info(
                    f"性能统计 - 总体执行时间(异常): {time.time() - total_start_time:.4f} 秒"
                )
            except:
                # 如果total_start_time未定义，则忽略
                pass

            new_data = {"risk_state": False, "risk_reason": error_msg}
            return (message_data, new_data), error_msg
