#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化浮点数坐标改进效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import math
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建模拟地图类
class MockMap3D:
    def __init__(self):
        self.height = 1000
        self.width = 1000
        self.depth = 100
        self.obstacle_manager = MockObstacleManager()
        self.grid_converter = MockGridConverter()
        self.non_traversable = set()
    
    def traversable(self, y, x, z):
        return (y, x, z) not in self.non_traversable

class MockObstacleManager:
    def __init__(self):
        self.orbit_paths = {}
        self.orbit_quadrants = {}
    
    def get_type_at_position(self, pos):
        return []
    
    def get_orbit_path(self, zone_name):
        return []
    
    def get_orbit_quadrants(self, zone_name):
        return {}

class MockGridConverter:
    def relative_to_geo(self, y, x, z):
        return {"lat": 39.0 + y * 0.001, "lon": 116.0 + x * 0.001, "alt": z * 10}

def simulate_8_direction_movement(start, goal, step_size=1.0):
    """模拟传统8方向移动"""
    path = [start]
    current = list(start)
    
    while abs(current[0] - goal[0]) > 0.5 or abs(current[1] - goal[1]) > 0.5:
        # 计算8方向移动
        dy = goal[0] - current[0]
        dx = goal[1] - current[1]
        
        # 归一化为8个方向
        if dy > 0:
            dy = 1
        elif dy < 0:
            dy = -1
        else:
            dy = 0
            
        if dx > 0:
            dx = 1
        elif dx < 0:
            dx = -1
        else:
            dx = 0
        
        current[0] += dy * step_size
        current[1] += dx * step_size
        path.append(tuple(current))
        
        # 防止无限循环
        if len(path) > 1000:
            break
    
    return path

def test_movement_comparison():
    """比较浮点数移动和8方向移动"""
    print("开始移动方式比较测试...")
    
    from src.core.pathfinding.orbit import OrbitPathFinder
    
    # 创建模拟地图
    mock_map = MockMap3D()
    orbit_finder = OrbitPathFinder(mock_map)
    
    # 测试场景
    test_cases = [
        {
            "name": "对角线路径",
            "start": (100, 100, 50),
            "goal": (200, 300, 50),
        },
        {
            "name": "精确角度路径",
            "start": (100, 100, 50),
            "goal": (150, 350, 50),
        },
        {
            "name": "复杂角度路径",
            "start": (100, 100, 50),
            "goal": (180, 280, 50),
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n测试 {test_case['name']}")
        
        # 浮点数移动路径
        float_path, error = orbit_finder.find_path(
            start=test_case["start"],
            goal=test_case["goal"],
            min_height=50,
            agent_id="float_test",
            start_time=0
        )
        
        # 8方向移动路径
        eight_dir_path = simulate_8_direction_movement(
            test_case["start"], test_case["goal"]
        )
        
        if float_path:
            # 计算路径统计
            float_stats = calculate_path_stats(float_path, test_case["start"], test_case["goal"])
            eight_dir_stats = calculate_path_stats_simple(eight_dir_path, test_case["start"], test_case["goal"])
            
            print(f"  浮点数路径: {float_stats['length']} 节点, 效率 {float_stats['efficiency']:.1f}%")
            print(f"  8方向路径: {eight_dir_stats['length']} 节点, 效率 {eight_dir_stats['efficiency']:.1f}%")
            print(f"  效率提升: {float_stats['efficiency'] - eight_dir_stats['efficiency']:.1f}%")
            
            results.append({
                "case": test_case,
                "float_path": float_path,
                "eight_dir_path": eight_dir_path,
                "float_stats": float_stats,
                "eight_dir_stats": eight_dir_stats
            })
        else:
            print(f"  浮点数路径规划失败: {error}")
    
    return results

def calculate_path_stats(path, start, goal):
    """计算路径统计信息"""
    if not path:
        return {"length": 0, "efficiency": 0, "actual_distance": 0, "direct_distance": 0}
    
    # 计算直线距离
    direct_distance = math.sqrt(
        (goal[0] - start[0])**2 + (goal[1] - start[1])**2
    )
    
    # 计算实际路径距离
    actual_distance = 0
    for i in range(1, len(path)):
        actual_distance += math.sqrt(
            (path[i].y - path[i-1].y)**2 + (path[i].x - path[i-1].x)**2
        )
    
    efficiency = (direct_distance / actual_distance) * 100 if actual_distance > 0 else 0
    
    return {
        "length": len(path),
        "efficiency": efficiency,
        "actual_distance": actual_distance,
        "direct_distance": direct_distance
    }

def calculate_path_stats_simple(path, start, goal):
    """计算简单路径统计信息"""
    if not path:
        return {"length": 0, "efficiency": 0, "actual_distance": 0, "direct_distance": 0}
    
    # 计算直线距离
    direct_distance = math.sqrt(
        (goal[0] - start[0])**2 + (goal[1] - start[1])**2
    )
    
    # 计算实际路径距离
    actual_distance = 0
    for i in range(1, len(path)):
        actual_distance += math.sqrt(
            (path[i][0] - path[i-1][0])**2 + (path[i][1] - path[i-1][1])**2
        )
    
    efficiency = (direct_distance / actual_distance) * 100 if actual_distance > 0 else 0
    
    return {
        "length": len(path),
        "efficiency": efficiency,
        "actual_distance": actual_distance,
        "direct_distance": direct_distance
    }

def visualize_movement_comparison(results):
    """可视化移动方式比较"""
    fig, axes = plt.subplots(2, len(results), figsize=(5*len(results), 10))
    if len(results) == 1:
        axes = axes.reshape(-1, 1)
    
    for idx, result in enumerate(results):
        case = result["case"]
        float_path = result["float_path"]
        eight_dir_path = result["eight_dir_path"]
        float_stats = result["float_stats"]
        eight_dir_stats = result["eight_dir_stats"]
        
        # 上图：浮点数路径
        ax1 = axes[0, idx]
        
        # 绘制浮点数路径
        if float_path:
            path_x = [node.x for node in float_path]
            path_y = [node.y for node in float_path]
            ax1.plot(path_x, path_y, 'o-', color='blue', linewidth=2, markersize=2, 
                    label='浮点数路径', alpha=0.8)
        
        # 绘制理想直线
        ax1.plot([case["start"][1], case["goal"][1]], [case["start"][0], case["goal"][0]], 
                '--', color='green', linewidth=2, alpha=0.7, label='理想直线')
        
        # 标记起点和终点
        ax1.plot(case["start"][1], case["start"][0], 's', color='red', markersize=8, 
                label='起点', markeredgecolor='black')
        ax1.plot(case["goal"][1], case["goal"][0], '^', color='orange', markersize=8, 
                label='终点', markeredgecolor='black')
        
        ax1.set_title(f'{case["name"]} - 浮点数移动\n效率: {float_stats["efficiency"]:.1f}%')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_aspect('equal')
        
        # 下图：8方向路径
        ax2 = axes[1, idx]
        
        # 绘制8方向路径
        if eight_dir_path:
            path_x = [point[1] for point in eight_dir_path]
            path_y = [point[0] for point in eight_dir_path]
            ax2.plot(path_x, path_y, 's-', color='red', linewidth=2, markersize=2, 
                    label='8方向路径', alpha=0.8)
        
        # 绘制理想直线
        ax2.plot([case["start"][1], case["goal"][1]], [case["start"][0], case["goal"][0]], 
                '--', color='green', linewidth=2, alpha=0.7, label='理想直线')
        
        # 标记起点和终点
        ax2.plot(case["start"][1], case["start"][0], 's', color='red', markersize=8, 
                label='起点', markeredgecolor='black')
        ax2.plot(case["goal"][1], case["goal"][0], '^', color='orange', markersize=8, 
                label='终点', markeredgecolor='black')
        
        ax2.set_title(f'{case["name"]} - 8方向移动\n效率: {eight_dir_stats["efficiency"]:.1f}%')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_aspect('equal')
        
        # 设置相同的坐标范围
        x_min = min(case["start"][1], case["goal"][1]) - 20
        x_max = max(case["start"][1], case["goal"][1]) + 20
        y_min = min(case["start"][0], case["goal"][0]) - 20
        y_max = max(case["start"][0], case["goal"][0]) + 20
        
        ax1.set_xlim(x_min, x_max)
        ax1.set_ylim(y_min, y_max)
        ax2.set_xlim(x_min, x_max)
        ax2.set_ylim(y_min, y_max)
    
    plt.tight_layout()
    filename = 'movement_comparison.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"\n移动方式比较图已保存为 '{filename}'")
    plt.close()

def create_angle_precision_visualization():
    """创建角度精度可视化"""
    print("\n创建角度精度可视化...")
    
    from src.core.pathfinding.orbit import OrbitPathFinder
    
    mock_map = MockMap3D()
    orbit_finder = OrbitPathFinder(mock_map)
    
    # 测试不同角度的移动
    center = (200, 200, 50)
    radius = 100
    angles = np.linspace(0, 2*np.pi, 16, endpoint=False)  # 16个不同角度
    
    fig, ax = plt.subplots(1, 1, figsize=(10, 10))
    
    colors = plt.cm.tab20(np.linspace(0, 1, len(angles)))
    
    for i, angle in enumerate(angles):
        # 计算目标点
        goal_y = center[0] + radius * np.sin(angle)
        goal_x = center[1] + radius * np.cos(angle)
        goal = (goal_y, goal_x, 50)
        
        # 测试单步移动
        next_pos, _, _ = orbit_finder._direct_move_towards_goal(
            (float(center[0]), float(center[1]), float(center[2])),
            (float(goal[0]), float(goal[1]), float(goal[2])),
            0, 50, "test", None, None, 1.0
        )
        
        if next_pos:
            # 绘制移动向量
            ax.arrow(center[1], center[0], 
                    next_pos[1] - center[1], next_pos[0] - center[0],
                    head_width=3, head_length=2, fc=colors[i], ec=colors[i],
                    label=f'{math.degrees(angle):.0f}°')
            
            # 绘制理想方向
            ideal_dx = goal[1] - center[1]
            ideal_dy = goal[0] - center[0]
            ideal_length = math.sqrt(ideal_dx**2 + ideal_dy**2)
            ideal_dx_norm = ideal_dx / ideal_length
            ideal_dy_norm = ideal_dy / ideal_length
            
            ax.plot([center[1], center[1] + ideal_dx_norm * 10], 
                   [center[0], center[0] + ideal_dy_norm * 10],
                   '--', color=colors[i], alpha=0.5, linewidth=1)
    
    # 绘制中心点
    ax.plot(center[1], center[0], 'ko', markersize=8, label='起点')
    
    # 绘制8方向参考线
    eight_directions = [(0, 1), (1, 1), (1, 0), (1, -1), (0, -1), (-1, -1), (-1, 0), (-1, 1)]
    for dy, dx in eight_directions:
        ax.plot([center[1], center[1] + dx * 15], [center[0], center[0] + dy * 15],
               'k:', alpha=0.3, linewidth=1)
    
    ax.set_xlim(center[1] - 30, center[1] + 30)
    ax.set_ylim(center[0] - 30, center[0] + 30)
    ax.set_xlabel('X坐标')
    ax.set_ylabel('Y坐标')
    ax.set_title('浮点数坐标角度精度测试\n实线：浮点数移动，虚线：理想方向，点线：8方向限制')
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')
    
    plt.tight_layout()
    filename = 'angle_precision.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"角度精度图已保存为 '{filename}'")
    plt.close()

def create_efficiency_comparison_chart(results):
    """创建效率比较图表"""
    print("\n创建效率比较图表...")
    
    case_names = [r["case"]["name"] for r in results]
    float_efficiencies = [r["float_stats"]["efficiency"] for r in results]
    eight_dir_efficiencies = [r["eight_dir_stats"]["efficiency"] for r in results]
    
    x = np.arange(len(case_names))
    width = 0.35
    
    fig, ax = plt.subplots(figsize=(10, 6))
    
    bars1 = ax.bar(x - width/2, float_efficiencies, width, label='浮点数移动', color='blue', alpha=0.7)
    bars2 = ax.bar(x + width/2, eight_dir_efficiencies, width, label='8方向移动', color='red', alpha=0.7)
    
    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}%', ha='center', va='bottom')
    
    for bar in bars2:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}%', ha='center', va='bottom')
    
    ax.set_xlabel('测试场景')
    ax.set_ylabel('路径效率 (%)')
    ax.set_title('浮点数坐标 vs 8方向移动 - 路径效率比较')
    ax.set_xticks(x)
    ax.set_xticklabels(case_names)
    ax.legend()
    ax.grid(True, alpha=0.3, axis='y')
    
    # 计算平均改进
    avg_improvement = np.mean([f - e for f, e in zip(float_efficiencies, eight_dir_efficiencies)])
    ax.text(0.02, 0.98, f'平均效率提升: {avg_improvement:.1f}%', 
           transform=ax.transAxes, fontsize=12, fontweight='bold',
           bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.7),
           verticalalignment='top')
    
    plt.tight_layout()
    filename = 'efficiency_comparison.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"效率比较图已保存为 '{filename}'")
    plt.close()

def main():
    """主函数"""
    print("=" * 60)
    print("浮点数坐标改进效果可视化")
    print("=" * 60)
    
    # 测试移动方式比较
    results = test_movement_comparison()
    
    if results:
        # 可视化移动方式比较
        visualize_movement_comparison(results)
        
        # 创建效率比较图表
        create_efficiency_comparison_chart(results)
    
    # 创建角度精度可视化
    create_angle_precision_visualization()
    
    print("\n" + "=" * 60)
    print("可视化完成！生成的图片文件:")
    print("- movement_comparison.png: 移动方式对比")
    print("- efficiency_comparison.png: 效率比较图表")
    print("- angle_precision.png: 角度精度测试")
    print("=" * 60)

if __name__ == "__main__":
    main()
