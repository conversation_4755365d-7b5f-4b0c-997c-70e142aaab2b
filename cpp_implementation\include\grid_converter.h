#ifndef GRID_CONVERTER_H
#define GRID_CONVERTER_H

#include <string>
#include <vector>
#include <cmath> // For M_PI, round, cos, etc.
#include <tuple> // For std::tuple

// It's good practice to define M_PI if not already defined (e.g. by cmath on some compilers/standards)
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace CVToolbox
{ // Using the same namespace as geometry_types

    // Structure to hold map configuration parameters relevant to grid conversion
    struct MapGridConfig
    {
        double ref_lat = 0.0;           // Reference latitude for the origin of the grid
        double ref_lon = 0.0;           // Reference longitude for the origin of the grid
        double ref_alt_msl = 0.0;       // Reference altitude (MSL) for z=0 in the grid
        double cell_size_lat_deg = 0.0; // Grid cell size in latitude degrees
        double cell_size_lon_deg = 0.0; // Grid cell size in longitude degrees
        double cell_size_alt_m = 1.0;   // Grid cell size in altitude meters (default to 1m if not specified)
        // Map dimensions in grid units (optional here, but Map3D will use them)
        // int map_height_grids = 0;
        // int map_width_grids = 0;
        // int map_depth_grids = 0;
    };

    class GridConverterCpp
    {
    public:
        GridConverterCpp(const MapGridConfig &config);

        // Geo (lat, lon, alt_msl) to relative grid (y, x, z) as doubles
        std::tuple<double, double, double> geo_to_relative_double(double lat, double lon, double alt_msl) const;

        // Geo (lat, lon, alt_msl) to relative grid (y, x, z) as integers (rounded)
        std::tuple<int, int, int> geo_to_relative_int(double lat, double lon, double alt_msl) const;

        // Relative grid (y, x, z) as doubles to Geo (lat, lon, alt_msl)
        std::tuple<double, double, double> relative_to_geo_double(double grid_y, double grid_x, double grid_z) const;

        // Relative grid (y, x, z) as integers to Geo (lat, lon, alt_msl) - center of grid cell
        std::tuple<double, double, double> relative_to_geo_int(int grid_y, int grid_x, int grid_z) const;

        const MapGridConfig &get_config() const { return config_; }

    private:
        MapGridConfig config_;
        // Pre-calculated constants for efficiency (if any)
        // For simple linear conversion based on cell degrees, these might not be strictly needed
        // but could be useful if more complex projections were involved.

        static double to_radians(double degrees)
        {
            return degrees * M_PI / 180.0;
        }
        // static double to_degrees(double radians) { // Not directly used in current conversion logic
        //     return radians * 180.0 / M_PI;
        // }
    };

} // namespace CVToolbox
#endif // GRID_CONVERTER_H
