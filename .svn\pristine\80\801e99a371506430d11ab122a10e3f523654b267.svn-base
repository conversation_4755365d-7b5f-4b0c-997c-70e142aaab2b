<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="GrazieInspection" enabled="false" level="GRAMMAR_ERROR" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="38">
            <item index="0" class="java.lang.String" itemvalue="monai" />
            <item index="1" class="java.lang.String" itemvalue="tqdm" />
            <item index="2" class="java.lang.String" itemvalue="nibabel" />
            <item index="3" class="java.lang.String" itemvalue="tensorboardX" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="einops" />
            <item index="6" class="java.lang.String" itemvalue="latent-diffusion" />
            <item index="7" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="8" class="java.lang.String" itemvalue="transformers" />
            <item index="9" class="java.lang.String" itemvalue="taming-transformers" />
            <item index="10" class="java.lang.String" itemvalue="timm" />
            <item index="11" class="java.lang.String" itemvalue="cityscapesscripts" />
            <item index="12" class="java.lang.String" itemvalue="imageio-ffmpeg" />
            <item index="13" class="java.lang.String" itemvalue="omegaconf" />
            <item index="14" class="java.lang.String" itemvalue="kornia" />
            <item index="15" class="java.lang.String" itemvalue="pytorch-lightning" />
            <item index="16" class="java.lang.String" itemvalue="torch-fidelity" />
            <item index="17" class="java.lang.String" itemvalue="interrogate" />
            <item index="18" class="java.lang.String" itemvalue="pudb" />
            <item index="19" class="java.lang.String" itemvalue="test-tube" />
            <item index="20" class="java.lang.String" itemvalue="invisible-watermark" />
            <item index="21" class="java.lang.String" itemvalue="xdoctest" />
            <item index="22" class="java.lang.String" itemvalue="clip" />
            <item index="23" class="java.lang.String" itemvalue="diffusers" />
            <item index="24" class="java.lang.String" itemvalue="imageio" />
            <item index="25" class="java.lang.String" itemvalue="pytest" />
            <item index="26" class="java.lang.String" itemvalue="streamlit" />
            <item index="27" class="java.lang.String" itemvalue="codecov" />
            <item index="28" class="java.lang.String" itemvalue="flake8" />
            <item index="29" class="java.lang.String" itemvalue="imagecorruptions" />
            <item index="30" class="java.lang.String" itemvalue="emoji" />
            <item index="31" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="32" class="java.lang.String" itemvalue="cython" />
            <item index="33" class="java.lang.String" itemvalue="fairscale" />
            <item index="34" class="java.lang.String" itemvalue="scipy" />
            <item index="35" class="java.lang.String" itemvalue="calflops" />
            <item index="36" class="java.lang.String" itemvalue="faster-coco-eval" />
            <item index="37" class="java.lang.String" itemvalue="tensorboard" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E501" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="trimesh.voxel.ops" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="SpellCheckingInspection" enabled="false" level="TYPO" enabled_by_default="false">
      <option name="processCode" value="true" />
      <option name="processLiterals" value="true" />
      <option name="processComments" value="true" />
    </inspection_tool>
  </profile>
</component>