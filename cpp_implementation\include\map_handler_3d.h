#ifndef MAP_HANDLER_3D_H
#define MAP_HANDLER_3D_H

#include <string>
#include <vector>
#include <set>
#include <map>
#include <tuple>
#include <optional>
#include <stdexcept> // For exceptions

#include "grid_converter.h"
#include "obstacle_type_manager.h"
#include "polygon_index.h"
#include "geometry_types.h"
#include "geometry_utils.h"

// Forward declare cnpy related types if used directly, or include cnpy.h
// For now, assume cnpy usage is encapsulated in .cpp
// namespace cnpy { struct NpyArray; }

namespace CVToolbox
{

    // NFZ (No-Fly Zone) geometric definition
    enum class NFZShapeType
    {
        CYLINDER,
        POLYGON
    };
    struct NFZDefinition
    {
        std::string id;
        NFZShapeType shape_type;
        Polygon2D vertices_grid_2d;    // For polygons (y,x), in grid coordinates
        Point2D center_grid_2d;        // For cylinders (y,x), in grid coordinates
        double radius_grid;            // For cylinders, radius in grid units (not squared, for clarity)
        int min_z_grid;                // Min altitude layer (inclusive)
        int max_z_grid;                // Max altitude layer (inclusive)
        bool rasterized_boundary_only; // True if only the boundary points were added to non_traversable
        std::string description;
    };

    // Configuration for MapHandler3DCpp
    struct MapHandler3DConfig
    {
        int map_height_grids;
        int map_width_grids;
        int map_depth_grids;
        MapGridConfig grid_converter_config; // From grid_converter.h
        std::string fixed_obstacles_cache_path;
        bool load_fixed_obstacles = true;
    };

    class MapHandler3DCpp
    {
    public:
        MapHandler3DCpp(const MapHandler3DConfig &config);
        // No explicit destructor needed unless managing raw pointers or specific library resources

        // --- Core Grid Operations ---
        bool is_in_bounds_grid(int y, int x, int z) const;
        bool is_traversable_grid(int y, int x, int z) const; // Checks non_traversable_ set
        std::vector<GridPoint3D> get_neighbors_grid(int y, int x, int z, bool allow_diagonal = false) const;

        // --- NFZ Management (Grid Coordinates directly) ---
        bool add_solid_cylindrical_nfz_grid(
            const std::string &zone_id,
            const Point2D &center_yx_grid, double radius_grid_units,
            int min_z_grid, int max_z_grid,
            bool rasterize_boundary_only = true, const std::string &description = "cylinder_nfz");

        bool add_solid_polygon_nfz_grid(
            const std::string &zone_id,
            const Polygon2D &vertices_yx_grid, // List of (y,x) points in grid coordinates
            int min_z_grid, int max_z_grid,
            bool rasterize_boundary_only = true, const std::string &description = "polygon_nfz");

        bool remove_nfz(const std::string &zone_id);

        // --- NFZ Management (Geo Coordinates, uses GridConverterCpp) ---
        bool add_solid_cylindrical_nfz_geo(
            const std::string &zone_id,
            double center_lat, double center_lon, double radius_meters,
            double min_alt_msl, double max_alt_msl,
            bool rasterize_boundary_only = true, const std::string &description = "cylinder_nfz");

        bool add_solid_polygon_nfz_geo(
            const std::string &zone_id,
            const std::vector<std::pair<double, double>> &vertices_lat_lon, // List of (lat,lon)
            double min_alt_msl, double max_alt_msl,
            double buffer_distance_meters = 0.0,
            bool rasterize_boundary_only = true, const std::string &description = "polygon_nfz");

        // --- Collision/Conflict Checks ---
        bool check_path_conflicts_with_obstacles_geo(const std::vector<Point3D> &path_geo_xyz) const;
        bool check_path_conflicts_with_obstacles_grid(const std::vector<GridPoint3D> &path_grid_yxz) const;

        bool is_point_inside_any_nfz_2d_grid(const Point2D &point_yx_grid) const;
        std::vector<bool> are_points_inside_any_nfz_2d_grid_batch(const std::vector<Point2D> &points_yx_grid) const;
        std::vector<std::string> get_nfz_ids_for_point_2d_grid(const Point2D &point_yx_grid) const;

        // --- Accessors ---
        const GridConverterCpp &get_grid_converter() const { return grid_converter_; }
        const ObstacleTypeManagerCpp &get_obstacle_manager() const { return obstacle_manager_; }
        const std::set<GridPoint3D> &get_non_traversable_set() const { return non_traversable_; }
        const std::map<std::string, NFZDefinition> &get_nfz_definitions() const { return nfz_definitions_; }
        MapHandler3DConfig get_config() const { return config_; }

    private:
        MapHandler3DConfig config_;
        GridConverterCpp grid_converter_;
        ObstacleTypeManagerCpp obstacle_manager_;
        PolygonIndexCpp polygon_index_2d_; // For 2D NFZ polygon checks (stores polygons in grid coordinates)

        std::set<GridPoint3D> non_traversable_;
        std::map<std::string, NFZDefinition> nfz_definitions_;

        void load_fixed_obstacles_from_cache();

        // Internal rasterization helpers (porting boundary_extractor.py logic)
        std::vector<GridPoint3D> rasterize_cylinder_3d(
            const Point2D &center_yx_grid, double radius_grid_units,
            int min_z_grid, int max_z_grid, bool boundary_only);

        std::vector<GridPoint3D> rasterize_polygon_3d(
            const Polygon2D &vertices_yx_grid,
            int min_z_grid, int max_z_grid, bool boundary_only);

        void add_obstacle_points_to_map(const std::string &type_name, const std::vector<GridPoint3D> &points, const std::string &description);
        void remove_obstacle_points_from_map(const std::string &type_name, const std::vector<GridPoint3D> &points);
    };

} // namespace CVToolbox
#endif // MAP_HANDLER_3D_H
