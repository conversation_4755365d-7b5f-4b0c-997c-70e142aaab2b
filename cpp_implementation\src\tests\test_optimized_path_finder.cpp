#include "optimized_path_finder.h"
#include "occupancy_map.h"
#include "grid_node.h"
#include "grid_node_data.h" // For Constraint
#include <iostream>
#include <vector>
#include <string>
#include <cassert> // For assert

// Helper function to print a path
void print_path(const std::vector<GridNode> &path, const std::string &path_name)
{
    std::cout << path_name << " (" << path.size() << " points):" << std::endl;
    if (path.empty())
    {
        std::cout << "  (empty)" << std::endl;
        return;
    }
    for (const auto &p : path)
    {
        std::cout << "  (y:" << p.y << ", x:" << p.x << ", z:" << p.z << ", t:" << p.t << ")" << std::endl;
    }
}

void test_simple_straight_path()
{
    std::cout << "\n--- Test: Simple Straight Path ---" << std::endl;
    OccupancyMap map(50, 50, 50, 1.0f); // 50x50x50 map
    OptimizedPathFinder finder(map, 1.0, 2.0, 1.0, false, 10, false, 0.7);

    GridNode start(5.0f, 5.0f, 10.0f); // Start at cruise altitude
    GridNode goal(5.0f, 15.0f, 10.0f); // Goal at cruise altitude
    int min_height = 10;
    std::string agent_id = "agent1";
    std::vector<Constraint> constraints;

    auto result = finder.find_path(start, goal, min_height, agent_id, constraints);
    const auto &complete_path = result.first.first;
    const auto &turning_points = result.first.second;
    const auto &error_msg = result.second;

    if (error_msg)
    {
        std::cout << "Error: " << *error_msg << std::endl;
    }
    assert(!error_msg && "Test failed: Simple path should not produce an error.");
    assert(!complete_path.empty() && "Test failed: Complete path should not be empty.");

    print_path(complete_path, "Complete Path");
    print_path(turning_points, "Turning Points");

    // Basic checks
    assert(complete_path.front().y == start.y && complete_path.front().x == start.x && complete_path.front().z == start.z);
    assert(complete_path.back().y == goal.y && complete_path.back().x == goal.x && complete_path.back().z == goal.z);
    std::cout << "Simple Straight Path test PASSED (basic checks)." << std::endl;
}

void test_path_with_takeoff_and_landing()
{
    std::cout << "\n--- Test: Path with Takeoff and Landing ---" << std::endl;
    OccupancyMap map(50, 50, 50, 1.0f);
    OptimizedPathFinder finder(map, 1.0, 2.0, 1.0, false, 10, false, 0.7);

    GridNode start(5.0f, 5.0f, 0.0f); // Start on ground
    GridNode goal(5.0f, 15.0f, 0.0f); // Goal on ground
    int min_height = 10;
    std::string agent_id = "agent2";
    std::vector<Constraint> constraints;

    auto result = finder.find_path(start, goal, min_height, agent_id, constraints);
    const auto &complete_path = result.first.first;
    const auto &error_msg = result.second;

    if (error_msg)
    {
        std::cout << "Error: " << *error_msg << std::endl;
    }
    assert(!error_msg && "Test failed: Takeoff/Landing path should not produce an error.");
    assert(!complete_path.empty() && "Test failed: Complete path should not be empty for takeoff/landing.");

    print_path(complete_path, "Complete Path (Takeoff/Landing)");

    assert(complete_path.front().z == start.z); // Starts at ground
    assert(complete_path.back().z == goal.z);   // Ends at ground
    bool reached_min_height = false;
    for (const auto &p : complete_path)
    {
        if (std::fabs(p.z - min_height) < 1e-5)
        {
            reached_min_height = true;
            break;
        }
    }
    assert(reached_min_height && "Test failed: Path should reach minimum cruise height.");
    std::cout << "Path with Takeoff and Landing test PASSED (basic checks)." << std::endl;
}

void test_no_path_due_to_obstacle()
{
    std::cout << "\n--- Test: No Path Due to Obstacle ---" << std::endl;
    OccupancyMap map(20, 20, 20, 1.0f);
    // Create a wall
    for (int y = 0; y < 20; ++y)
    {
        for (int z = 0; z < 20; ++z)
        {
            map.set_obstacle(y, 10, z); // Wall at x=10
        }
    }

    OptimizedPathFinder finder(map, 1.0, 2.0, 1.0, false, 5, false, 0.7); // Reduced max_jps_steps for faster fail

    GridNode start(5.0f, 5.0f, 5.0f);
    GridNode goal(5.0f, 15.0f, 5.0f);
    int min_height = 5;
    std::string agent_id = "agent3";
    std::vector<Constraint> constraints;

    auto result = finder.find_path(start, goal, min_height, agent_id, constraints);
    const auto &complete_path = result.first.first;
    const auto &error_msg = result.second;

    assert(error_msg && "Test failed: Expected an error message for no path.");
    if (error_msg)
    {
        std::cout << "Expected error: " << *error_msg << std::endl;
    }
    assert(complete_path.empty() && "Test failed: Path should be empty when no path found.");
    std::cout << "No Path Due to Obstacle test PASSED." << std::endl;
}

int main()
{
    std::cout << "Starting OptimizedPathFinder C++ Tests..." << std::endl;

    test_simple_straight_path();
    test_path_with_takeoff_and_landing();
    test_no_path_due_to_obstacle();
    // Add more tests for smoothing, turning points, constraints, etc.

    std::cout << "\nAll OptimizedPathFinder C++ tests finished." << std::endl;
    return 0;
}
