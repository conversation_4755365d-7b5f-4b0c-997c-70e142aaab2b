#pragma once

#include <string>
#include <optional> // Required for std::optional

/**
 * @brief 时间区间类
 * 对应Python中的TimeInterval类
 */
class TimeInterval
{
public:
    int start;
    int end;
    std::string agent_id;

    /**
     * @brief 构造函数
     * @param start_time 开始时间
     * @param end_time 结束时间
     * @param id 智能体ID
     */
    TimeInterval(int start_time, int end_time, const std::string &id);

    /**
     * @brief 检查两个时间区间是否重叠
     * @param other 另一个时间区间
     * @return 是否重叠
     */
    bool overlaps(const TimeInterval &other) const;

    /**
     * @brief 如果两个区间重叠或连续且属于同一个agent，则合并它们
     * @param other 另一个时间区间
     * @return 合并后的时间区间，如果不能合并则返回std::nullopt
     */
    std::optional<TimeInterval> merge(const TimeInterval &other) const;
};
