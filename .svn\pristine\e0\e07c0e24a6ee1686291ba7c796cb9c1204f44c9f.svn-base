# 路径规划服务打包指南

## 环境准备

### 1. 安装Miniconda
确保系统已安装Miniconda或Anaconda。

### 2. 创建专用环境
```bash
# 创建Python 3.8环境
conda create -n forpyinstall python=3.8.12
# 激活环境
conda activate forpyinstall
```

## 依赖安装

### 1. 使用requirements.txt
```bash
# 安装所有项目依赖
pip install -r requirements.txt

# 安装PyInstaller
pip install pyinstaller==6.12.0
```

### 2. 验证numpy版本
```bash
# 确认numpy版本正确安装
python -c "import numpy; print(numpy.__version__)"
# 应显示1.24.4或兼容版本
```

## 打包命令

### 1. 环境验证
```bash
# 确保在正确的conda环境中
conda activate forpyinstall
# 验证Python路径
which python  # 应显示conda环境路径
```

### 2. 清理旧构建
```bash
rm -rf build/ dist/ __pycache__/ *.spec
```

### 3. 执行打包
```bash
# 获取conda python路径
CONDA_PYTHON=$(which python)

# 执行打包命令
# $CONDA_PYTHON -m PyInstaller \
# --name route_planning_service \
# --onefile \
# --hidden-import=kafka \
# --hidden-import=redis \
# --hidden-import=numpy.core._multiarray_umath \
# --hidden-import=numpy.core._umath_tests \
# --hidden-import=mysql.connector \
# --hidden-import=paho.mqtt \
# --hidden-import=termcolor \
# --hidden-import=dotenv \
# --add-data "src/core/map/cache/chongqing/grid_mappings.json:cache/chongqing" \
# --add-data "src/core/map/cache/chongqing/fixed_obstacles_cache.npz:cache/chongqing" \
# --collect-all numpy \
# --collect-all matplotlib \
# main.py

# 通用打包命令 (包含主配置文件和所有必需的城市缓存)
$CONDA_PYTHON -m PyInstaller \
--name route_planning_service \
--onefile \
--hidden-import=kafka \
--hidden-import=redis \
--hidden-import=numpy \
--hidden-import=matplotlib \
--hidden-import=mysql.connector \
--hidden-import=mysql.connector.locales.eng.client_error \
--hidden-import=paho.mqtt \
--hidden-import=termcolor \
--hidden-import=dotenv \
--add-data "src/core/map/cache/chongqing/fixed_obstacles_cache.npz:cache/chongqing" \
# --add-data "src/core/map/cache/nanjing/YOUR_CACHE_FILE.npz:cache/nanjing" \  # 如果南京有必需缓存，取消注释并修改
# --add-data "src/core/map/cache/shaoquan/YOUR_CACHE_FILE.npz:cache/shaoquan" \ # 如果韶关有必需缓存，取消注释并修改
# 注意：青岛 (qingdao) 目录未在当前项目中找到，如有需要请添加
--collect-all numpy \
--collect-all matplotlib \
--paths "/home/<USER>/miniconda3/envs/forpyinstall/lib/python3.8/site-packages" \
main.py
```

### 4. 验证打包结果
```bash
# 给可执行文件添加权限
chmod +x route_planning_service

# 运行打包后的程序（使用默认配置）
./route_planning_service

# 使用命令行参数指定配置
./route_planning_service --location shijiazhuang --server 10.18.64.36

# 指定配置文件路径
./route_planning_service --config /path/to/your/config.json

# 组合使用参数
./route_planning_service --config /path/to/your/config.json --location shijiazhuang --server 10.18.64.36
```

## 常见问题处理

### 1. numpy导入错误
如果遇到numpy相关的导入错误：
```bash
conda install -c conda-forge numpy=1.24.4
conda clean --all
```

### 2. 环境问题
- 确保终端显示(forpyinstall)环境名
- 使用`which python`确认使用的是conda环境中的Python
- 不要在系统Python环境中运行PyInstaller

### 3. 资源文件路径问题
- 确保使用正确的相对路径结构
- 配置文件 `config.json` 应放置在可执行文件同一目录下，不要将其打包到可执行文件中
- 打包后城市缓存文件位置示例：`cache/chongqing/`

### 4. 其他注意事项
1. 确保所有资源文件引用使用get_resource_path()函数：
```python
def get_resource_path(relative_path):
    """获取打包后资源文件的绝对路径"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)
```

2. 资源文件访问示例：
```python
# 访问主配置文件 - 不要使用get_resource_path，直接从当前目录加载
config_path = "config.json"  # 配置文件应放在可执行文件同一目录下

# 访问城市特定缓存文件 (示例)
# 注意：实际路径应根据 config.json 中的配置动态构建
cache_file_relative_path = os.path.join("cache", city_name, "fixed_obstacles_cache.npz") # city_name 来自配置
cache_path = get_resource_path(cache_file_relative_path)
```

## 维护说明

### 1. 依赖更新
当添加新的依赖时：
1. 更新requirements.txt
2. 在打包命令中添加相应的--hidden-import参数（如需要）

### 2. 资源文件更新
当添加新的资源文件（如新的城市缓存 `.npz` 文件）时：
1. 在通用的打包命令中添加对应的 `--add-data "path/to/your/file:destination/in/package"` 参数。
2. 确保代码中使用 `get_resource_path()` 函数，并结合 `config.json` 中的配置来动态加载正确的资源文件。
3. 如果添加了主配置文件 `config.json` 本身需要引用的其他类型文件（例如，如果 `config.json` 引用了一个 `.yaml` 文件），请确保该文件也被放置在可执行文件同一目录下。

### 3. 配置文件处理
**重要说明**：
- **不要**将 `config.json` 打包到可执行文件中，应将其放置在可执行文件同一目录下
- 这样做的好处是可以在不重新打包的情况下修改配置
- 如果将 `config.json` 打包到可执行文件中，修改外部的配置文件将不会生效，因为程序会优先读取打包内部的配置文件
- 程序支持通过命令行参数指定配置：
  - `--config`: 指定配置文件路径
  - `--location`: 指定地点（如 nanjing, shijiazhuang）
  - `--server`: 指定服务器地址
  - 命令行参数的优先级高于配置文件中的设置

### 4. 打包配置维护
可以考虑创建.spec文件来管理复杂的打包配置：
```bash
# 生成.spec文件
pyi-makespec [上述打包参数] main.py

# 使用.spec文件打包
pyinstaller route_planning_service.spec
```
